@echo off
chcp 65001 >nul
echo ========================================
echo 上海房地产数据统计小程序 - 真实数据版
echo ========================================
echo.

echo ✅ Tushare Token 已配置: e895300478478bcaa01c96bc2a6fb7bd0f81dc3f5fcc2b71e69bdfb9
echo 🔴 真实数据模式已启用
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 📥 下载地址: https://nodejs.org/
    echo.
    echo 💡 或者直接查看演示页面:
    start "" "%~dp0real-data-demo.html"
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

echo 正在检查依赖包...
if not exist "node_modules" (
    echo 📦 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 错误: 依赖包安装失败
        echo 💡 查看演示页面:
        start "" "%~dp0real-data-demo.html"
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已存在
)

echo.
echo 🚀 正在启动开发服务器...
echo 📊 真实数据将通过Tushare API获取
echo 🌐 服务器启动后会自动打开浏览器
echo.
echo 📍 访问地址: http://localhost:3000
echo ⏹️  按 Ctrl+C 可以停止服务器
echo.

REM 先打开演示页面
echo 💡 正在打开真实数据演示页面...
start "" "%~dp0real-data-demo.html"

echo.
echo 🔄 正在启动完整React应用...
npm run dev

if %errorlevel% neq 0 (
    echo.
    echo ❌ React应用启动失败，但您可以查看演示页面
    echo 💡 演示页面已在浏览器中打开
    echo.
)

pause
