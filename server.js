const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;

const server = http.createServer((req, res) => {
  let filePath = req.url === '/' ? '/test.html' : req.url;
  const fullPath = path.join(__dirname, filePath);
  
  console.log(`请求: ${req.url} -> ${fullPath}`);
  
  fs.readFile(fullPath, (err, data) => {
    if (err) {
      console.error('文件读取错误:', err.message);
      res.writeHead(404, {'Content-Type': 'text/html; charset=utf-8'});
      res.end(`
        <html>
          <head><title>404 - 文件未找到</title></head>
          <body>
            <h1>404 - 文件未找到</h1>
            <p>请求的文件: ${filePath}</p>
            <p>完整路径: ${fullPath}</p>
            <p>错误信息: ${err.message}</p>
          </body>
        </html>
      `);
    } else {
      res.writeHead(200, {'Content-Type': 'text/html; charset=utf-8'});
      res.end(data);
    }
  });
});

server.listen(PORT, () => {
  console.log(`服务器启动成功！`);
  console.log(`访问地址: http://localhost:${PORT}`);
  console.log(`演示页面: http://localhost:${PORT}/test.html`);
});

server.on('error', (err) => {
  console.error('服务器错误:', err.message);
});
