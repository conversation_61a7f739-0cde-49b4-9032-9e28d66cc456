import React, { useState, useEffect } from 'react';
import { Home, Building, TrendingUp, BarChart3, PieChart, ArrowUpRight, ArrowDownRight } from 'lucide-react';

const SeparatedHousingData = ({ statisticsService }) => {
  const [housingData, setHousingData] = useState(null);
  const [selectedView, setSelectedView] = useState('overview'); // overview, secondHand, newHouse
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSeparatedData();
  }, []);

  const loadSeparatedData = async () => {
    setLoading(true);
    try {
      const stats = await statisticsService.getCurrentStats();
      setHousingData(stats);
    } catch (error) {
      console.error('加载分离数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num, type = 'number') => {
    if (!num) return '0';
    
    switch (type) {
      case 'price':
        return `¥${num.toLocaleString()}`;
      case 'volume':
        return `${num.toLocaleString()}套`;
      case 'area':
        return `${num.toLocaleString()}㎡`;
      case 'percentage':
        return `${num > 0 ? '+' : ''}${num.toFixed(1)}%`;
      case 'money':
        return `${num.toLocaleString()}万元`;
      default:
        return num.toLocaleString();
    }
  };

  const getChangeIcon = (value) => {
    return value > 0 ? <ArrowUpRight size={16} /> : <ArrowDownRight size={16} />;
  };

  const getChangeColor = (value) => {
    return value > 0 ? '#27ae60' : '#e74c3c';
  };

  if (loading) {
    return (
      <div className="chart-container">
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div style={{ fontSize: '1rem', color: '#666' }}>加载二手房和新房数据...</div>
        </div>
      </div>
    );
  }

  if (!housingData || !housingData.secondHand || !housingData.newHouse) {
    return (
      <div className="chart-container">
        <h3 className="chart-title">二手房 vs 新房数据</h3>
        <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
          暂无分离数据，请稍后重试
        </div>
      </div>
    );
  }

  const { secondHand, newHouse } = housingData;

  return (
    <div className="chart-container">
      <h3 className="chart-title">
        <PieChart size={20} style={{ marginRight: '0.5rem' }} />
        二手房 vs 新房数据对比 (2025年7月)
      </h3>

      {/* 视图切换 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        gap: '1rem', 
        marginBottom: '1.5rem',
        flexWrap: 'wrap'
      }}>
        {[
          { key: 'overview', label: '综合对比', icon: PieChart },
          { key: 'secondHand', label: '二手房', icon: Home },
          { key: 'newHouse', label: '新房', icon: Building }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setSelectedView(key)}
            style={{
              padding: '0.5rem 1rem',
              border: selectedView === key ? '2px solid #3498db' : '1px solid #ddd',
              background: selectedView === key ? '#3498db' : 'white',
              color: selectedView === key ? 'white' : '#333',
              borderRadius: '20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              fontSize: '0.9rem'
            }}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* 综合对比视图 */}
      {selectedView === 'overview' && (
        <div>
          {/* 市场份额 */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem',
            marginBottom: '1.5rem'
          }}>
            <div style={{ 
              background: '#f8f9fa', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h4 style={{ color: '#e74c3c', marginBottom: '0.5rem' }}>二手房市场份额</h4>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#e74c3c' }}>
                {secondHand.marketShare}%
              </div>
              <small>日成交量占比</small>
            </div>
            <div style={{ 
              background: '#f8f9fa', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h4 style={{ color: '#3498db', marginBottom: '0.5rem' }}>新房市场份额</h4>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
                {newHouse.marketShare}%
              </div>
              <small>日成交量占比</small>
            </div>
          </div>

          {/* 对比表格 */}
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ background: '#f8f9fa' }}>
                  <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #ddd' }}>指标</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #ddd', color: '#e74c3c' }}>二手房</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #ddd', color: '#3498db' }}>新房</th>
                  <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #ddd' }}>差值</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #eee' }}>平均价格</td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.avgPrice, 'price')}/㎡
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(newHouse.avgPrice, 'price')}/㎡
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(newHouse.avgPrice - secondHand.avgPrice, 'price')}/㎡
                  </td>
                </tr>
                <tr>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #eee' }}>日成交量</td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.dailyVolume, 'volume')}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(newHouse.dailyVolume, 'volume')}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.dailyVolume - newHouse.dailyVolume, 'volume')}
                  </td>
                </tr>
                <tr>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #eee' }}>月累计成交</td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.monthlyVolume, 'volume')}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(newHouse.monthlyVolume, 'volume')}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.monthlyVolume - newHouse.monthlyVolume, 'volume')}
                  </td>
                </tr>
                <tr>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #eee' }}>挂牌量</td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.listings, 'volume')}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(newHouse.listings, 'volume')}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {formatNumber(secondHand.listings - newHouse.listings, 'volume')}
                  </td>
                </tr>
                <tr>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #eee' }}>平均户型</td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {secondHand.avgUnitSize}㎡
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {newHouse.avgUnitSize}㎡
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #eee' }}>
                    {(newHouse.avgUnitSize - secondHand.avgUnitSize).toFixed(1)}㎡
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 二手房详细视图 */}
      {selectedView === 'secondHand' && (
        <div>
          <div style={{ 
            background: 'linear-gradient(45deg, #e74c3c, #c0392b)', 
            color: 'white', 
            padding: '1rem', 
            borderRadius: '8px', 
            marginBottom: '1rem',
            textAlign: 'center'
          }}>
            <h4>二手房市场数据 (2025年7月)</h4>
            <p>市场份额: {secondHand.marketShare}% | 主导地位明显</p>
          </div>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem' 
          }}>
            {[
              { label: '平均价格', value: secondHand.avgPrice, type: 'price', unit: '/㎡', change: secondHand.comparisons.priceYoY },
              { label: '日成交量', value: secondHand.dailyVolume, type: 'volume', change: secondHand.comparisons.volumeYoY },
              { label: '月累计成交', value: secondHand.monthlyVolume, type: 'volume', change: secondHand.comparisons.volumeYoY },
              { label: '挂牌量', value: secondHand.listings, type: 'volume', change: secondHand.comparisons.listingsYoY },
              { label: '平均户型', value: secondHand.avgUnitSize, type: 'number', unit: '㎡' },
              { label: '成交金额', value: secondHand.totalValue, type: 'money' }
            ].map((item, index) => (
              <div key={index} style={{ 
                background: 'white', 
                padding: '1rem', 
                borderRadius: '8px', 
                border: '1px solid #eee',
                textAlign: 'center'
              }}>
                <h5 style={{ color: '#666', marginBottom: '0.5rem' }}>{item.label}</h5>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#e74c3c', marginBottom: '0.5rem' }}>
                  {formatNumber(item.value, item.type)}{item.unit || ''}
                </div>
                {item.change !== undefined && (
                  <div style={{ 
                    fontSize: '0.9rem', 
                    color: getChangeColor(item.change),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.25rem'
                  }}>
                    {getChangeIcon(item.change)}
                    {formatNumber(item.change, 'percentage')} 同比
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 新房详细视图 */}
      {selectedView === 'newHouse' && (
        <div>
          <div style={{ 
            background: 'linear-gradient(45deg, #3498db, #2980b9)', 
            color: 'white', 
            padding: '1rem', 
            borderRadius: '8px', 
            marginBottom: '1rem',
            textAlign: 'center'
          }}>
            <h4>新房市场数据 (2025年7月)</h4>
            <p>市场份额: {newHouse.marketShare}% | 高端市场为主</p>
          </div>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem' 
          }}>
            {[
              { label: '平均价格', value: newHouse.avgPrice, type: 'price', unit: '/㎡', change: newHouse.comparisons.priceYoY },
              { label: '日成交量', value: newHouse.dailyVolume, type: 'volume', change: newHouse.comparisons.volumeYoY },
              { label: '月累计成交', value: newHouse.monthlyVolume, type: 'volume', change: newHouse.comparisons.volumeYoY },
              { label: '挂牌量', value: newHouse.listings, type: 'volume', change: newHouse.comparisons.listingsYoY },
              { label: '平均户型', value: newHouse.avgUnitSize, type: 'number', unit: '㎡' },
              { label: '成交金额', value: newHouse.totalValue, type: 'money' }
            ].map((item, index) => (
              <div key={index} style={{ 
                background: 'white', 
                padding: '1rem', 
                borderRadius: '8px', 
                border: '1px solid #eee',
                textAlign: 'center'
              }}>
                <h5 style={{ color: '#666', marginBottom: '0.5rem' }}>{item.label}</h5>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#3498db', marginBottom: '0.5rem' }}>
                  {formatNumber(item.value, item.type)}{item.unit || ''}
                </div>
                {item.change !== undefined && (
                  <div style={{ 
                    fontSize: '0.9rem', 
                    color: getChangeColor(item.change),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.25rem'
                  }}>
                    {getChangeIcon(item.change)}
                    {formatNumber(item.change, 'percentage')} 同比
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div style={{ 
        marginTop: '1rem', 
        padding: '1rem', 
        background: '#f8f9fa', 
        borderRadius: '8px',
        fontSize: '0.9rem',
        color: '#666'
      }}>
        <strong>数据说明:</strong> 二手房占据市场主导地位({secondHand.marketShare}%)，新房以高端项目为主({newHouse.marketShare}%)。
        二手房平均户型{secondHand.avgUnitSize}㎡，新房平均户型{newHouse.avgUnitSize}㎡。
        数据更新时间: {new Date().toLocaleString('zh-CN')}
      </div>
    </div>
  );
};

export default SeparatedHousingData;
