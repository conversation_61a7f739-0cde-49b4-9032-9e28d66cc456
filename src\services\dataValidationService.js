/**
 * 数据验证和质量检查服务
 * 确保获取的数据准确性和可靠性
 */

class DataValidationService {
  constructor() {
    // 上海房地产数据的合理范围
    this.validationRules = {
      avgPrice: {
        min: 30000,    // 最低30,000元/㎡
        max: 150000,   // 最高150,000元/㎡
        typical: { min: 50000, max: 90000 } // 典型范围
      },
      volume: {
        min: 0,        // 最低0套
        max: 1000,     // 最高1000套/日
        typical: { min: 50, max: 300 } // 典型范围
      },
      listings: {
        min: 1000,     // 最低1000套
        max: 20000,    // 最高20000套
        typical: { min: 6000, max: 12000 } // 典型范围
      },
      area: {
        min: 0,        // 最低0㎡
        max: 100000,   // 最高100,000㎡/日
        typical: { min: 5000, max: 25000 } // 典型范围
      }
    };

    // 数据质量评分权重
    this.qualityWeights = {
      completeness: 0.3,    // 完整性
      accuracy: 0.4,        // 准确性
      timeliness: 0.2,      // 时效性
      consistency: 0.1      // 一致性
    };
  }

  /**
   * 验证数据质量
   * @param {Object} data 待验证的数据
   * @returns {Object} 验证结果
   */
  validateData(data) {
    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      qualityScore: 0,
      details: {}
    };

    // 检查数据完整性
    const completeness = this.checkCompleteness(data);
    validation.details.completeness = completeness;

    // 检查数据准确性
    const accuracy = this.checkAccuracy(data);
    validation.details.accuracy = accuracy;

    // 检查数据时效性
    const timeliness = this.checkTimeliness(data);
    validation.details.timeliness = timeliness;

    // 检查数据一致性
    const consistency = this.checkConsistency(data);
    validation.details.consistency = consistency;

    // 计算总体质量分数
    validation.qualityScore = this.calculateQualityScore(validation.details);

    // 收集错误和警告
    this.collectIssues(validation);

    // 判断数据是否有效
    validation.isValid = validation.errors.length === 0 && validation.qualityScore >= 0.6;

    return validation;
  }

  /**
   * 检查数据完整性
   * @param {Object} data 数据对象
   * @returns {Object} 完整性检查结果
   */
  checkCompleteness(data) {
    const requiredFields = ['avgPrice', 'volume', 'listings', 'area'];
    const missingFields = [];
    const nullFields = [];

    requiredFields.forEach(field => {
      if (!(field in data)) {
        missingFields.push(field);
      } else if (data[field] === null || data[field] === undefined) {
        nullFields.push(field);
      }
    });

    const completenessScore = 1 - (missingFields.length + nullFields.length) / requiredFields.length;

    return {
      score: completenessScore,
      missingFields,
      nullFields,
      totalFields: requiredFields.length,
      validFields: requiredFields.length - missingFields.length - nullFields.length
    };
  }

  /**
   * 检查数据准确性
   * @param {Object} data 数据对象
   * @returns {Object} 准确性检查结果
   */
  checkAccuracy(data) {
    const fieldAccuracy = {};
    let totalScore = 0;
    let fieldCount = 0;

    Object.keys(this.validationRules).forEach(field => {
      if (field in data && data[field] !== null && data[field] !== undefined) {
        const value = data[field];
        const rules = this.validationRules[field];
        
        let score = 1;
        let issues = [];

        // 检查是否在绝对范围内
        if (value < rules.min || value > rules.max) {
          score = 0;
          issues.push(`值 ${value} 超出合理范围 [${rules.min}, ${rules.max}]`);
        }
        // 检查是否在典型范围内
        else if (value < rules.typical.min || value > rules.typical.max) {
          score = 0.7;
          issues.push(`值 ${value} 超出典型范围 [${rules.typical.min}, ${rules.typical.max}]`);
        }

        fieldAccuracy[field] = { score, issues, value };
        totalScore += score;
        fieldCount++;
      }
    });

    return {
      score: fieldCount > 0 ? totalScore / fieldCount : 0,
      fieldAccuracy,
      totalFields: fieldCount
    };
  }

  /**
   * 检查数据时效性
   * @param {Object} data 数据对象
   * @returns {Object} 时效性检查结果
   */
  checkTimeliness(data) {
    const now = new Date();
    let score = 1;
    let issues = [];

    if (data.lastUpdated) {
      const lastUpdate = new Date(data.lastUpdated);
      const ageInMinutes = (now - lastUpdate) / (1000 * 60);

      if (ageInMinutes > 60) { // 超过1小时
        score = 0.5;
        issues.push(`数据更新时间超过1小时 (${Math.round(ageInMinutes)}分钟前)`);
      } else if (ageInMinutes > 30) { // 超过30分钟
        score = 0.8;
        issues.push(`数据更新时间超过30分钟 (${Math.round(ageInMinutes)}分钟前)`);
      }
    } else {
      score = 0.3;
      issues.push('缺少数据更新时间戳');
    }

    return {
      score,
      issues,
      lastUpdated: data.lastUpdated,
      ageInMinutes: data.lastUpdated ? (now - new Date(data.lastUpdated)) / (1000 * 60) : null
    };
  }

  /**
   * 检查数据一致性
   * @param {Object} data 数据对象
   * @returns {Object} 一致性检查结果
   */
  checkConsistency(data) {
    let score = 1;
    let issues = [];

    // 检查成交金额与价格、面积的一致性
    if (data.avgPrice && data.area && data.totalValue) {
      const expectedValue = (data.avgPrice * data.area) / 10000; // 转换为万元
      const actualValue = data.totalValue;
      const deviation = Math.abs(expectedValue - actualValue) / expectedValue;

      if (deviation > 0.2) { // 偏差超过20%
        score = 0.5;
        issues.push(`成交金额与价格面积不一致，偏差${(deviation * 100).toFixed(1)}%`);
      } else if (deviation > 0.1) { // 偏差超过10%
        score = 0.8;
        issues.push(`成交金额与价格面积略有偏差，偏差${(deviation * 100).toFixed(1)}%`);
      }
    }

    // 检查成交量与成交面积的合理性
    if (data.volume && data.area) {
      const avgUnitSize = data.area / data.volume;
      if (avgUnitSize < 30 || avgUnitSize > 200) { // 平均户型不在30-200㎡范围内
        score = Math.min(score, 0.7);
        issues.push(`平均户型面积异常: ${avgUnitSize.toFixed(1)}㎡`);
      }
    }

    return {
      score,
      issues
    };
  }

  /**
   * 计算总体质量分数
   * @param {Object} details 各项检查详情
   * @returns {number} 质量分数 (0-1)
   */
  calculateQualityScore(details) {
    const weights = this.qualityWeights;
    
    return (
      details.completeness.score * weights.completeness +
      details.accuracy.score * weights.accuracy +
      details.timeliness.score * weights.timeliness +
      details.consistency.score * weights.consistency
    );
  }

  /**
   * 收集错误和警告信息
   * @param {Object} validation 验证对象
   */
  collectIssues(validation) {
    const { details } = validation;

    // 完整性问题
    if (details.completeness.missingFields.length > 0) {
      validation.errors.push(`缺少必需字段: ${details.completeness.missingFields.join(', ')}`);
    }
    if (details.completeness.nullFields.length > 0) {
      validation.warnings.push(`字段值为空: ${details.completeness.nullFields.join(', ')}`);
    }

    // 准确性问题
    Object.entries(details.accuracy.fieldAccuracy).forEach(([field, accuracy]) => {
      if (accuracy.score === 0) {
        validation.errors.push(`${field}: ${accuracy.issues.join(', ')}`);
      } else if (accuracy.score < 1) {
        validation.warnings.push(`${field}: ${accuracy.issues.join(', ')}`);
      }
    });

    // 时效性问题
    if (details.timeliness.score < 0.5) {
      validation.errors.push(...details.timeliness.issues);
    } else if (details.timeliness.score < 1) {
      validation.warnings.push(...details.timeliness.issues);
    }

    // 一致性问题
    if (details.consistency.score < 0.5) {
      validation.errors.push(...details.consistency.issues);
    } else if (details.consistency.score < 1) {
      validation.warnings.push(...details.consistency.issues);
    }
  }

  /**
   * 修正数据
   * @param {Object} data 原始数据
   * @param {Object} validation 验证结果
   * @returns {Object} 修正后的数据
   */
  correctData(data, validation) {
    const correctedData = { ...data };
    const corrections = [];

    // 修正超出范围的值
    Object.keys(this.validationRules).forEach(field => {
      if (field in correctedData && correctedData[field] !== null) {
        const value = correctedData[field];
        const rules = this.validationRules[field];

        if (value < rules.min) {
          correctedData[field] = rules.min;
          corrections.push(`${field} 值过小，已调整为最小值 ${rules.min}`);
        } else if (value > rules.max) {
          correctedData[field] = rules.max;
          corrections.push(`${field} 值过大，已调整为最大值 ${rules.max}`);
        }
      }
    });

    // 计算缺失的成交金额
    if (!correctedData.totalValue && correctedData.avgPrice && correctedData.area) {
      correctedData.totalValue = Math.round((correctedData.avgPrice * correctedData.area) / 10000);
      corrections.push('自动计算成交金额');
    }

    return {
      data: correctedData,
      corrections,
      originalData: data
    };
  }

  /**
   * 生成数据质量报告
   * @param {Object} validation 验证结果
   * @returns {Object} 质量报告
   */
  generateQualityReport(validation) {
    const { qualityScore, details } = validation;
    
    let qualityLevel;
    if (qualityScore >= 0.9) qualityLevel = '优秀';
    else if (qualityScore >= 0.7) qualityLevel = '良好';
    else if (qualityScore >= 0.5) qualityLevel = '一般';
    else qualityLevel = '较差';

    return {
      qualityLevel,
      qualityScore: Math.round(qualityScore * 100),
      summary: {
        completeness: Math.round(details.completeness.score * 100),
        accuracy: Math.round(details.accuracy.score * 100),
        timeliness: Math.round(details.timeliness.score * 100),
        consistency: Math.round(details.consistency.score * 100)
      },
      recommendations: this.generateRecommendations(validation),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 生成改进建议
   * @param {Object} validation 验证结果
   * @returns {Array} 建议列表
   */
  generateRecommendations(validation) {
    const recommendations = [];

    if (validation.details.completeness.score < 0.8) {
      recommendations.push('建议补充缺失的数据字段');
    }

    if (validation.details.accuracy.score < 0.8) {
      recommendations.push('建议验证数据源的准确性');
    }

    if (validation.details.timeliness.score < 0.8) {
      recommendations.push('建议增加数据更新频率');
    }

    if (validation.details.consistency.score < 0.8) {
      recommendations.push('建议检查数据计算逻辑');
    }

    if (validation.qualityScore < 0.6) {
      recommendations.push('建议使用多个数据源进行交叉验证');
    }

    return recommendations;
  }
}

// 创建单例实例
const dataValidationService = new DataValidationService();

export default dataValidationService;
