# 上海房地产数据统计小程序 - 环境变量配置示例
# 复制此文件为 .env 并填入真实的API密钥

# ===========================================
# 官方数据源API密钥
# ===========================================

# 上海市房屋管理局API密钥（如果有官方API）
REACT_APP_SHANGHAI_API_KEY=your_shanghai_api_key_here

# 国家统计局API密钥（如果需要）
REACT_APP_STATS_GOV_API_KEY=your_stats_gov_api_key_here

# ===========================================
# 第三方数据服务API密钥
# ===========================================

# AKShare数据接口（免费，但可能需要注册）
REACT_APP_AKSHARE_API_KEY=your_akshare_api_key_here

# Tushare数据接口（需要注册获取token）
# 注册地址: https://tushare.pro/register
REACT_APP_TUSHARE_TOKEN=your_tushare_token_here

# 聚合数据API（可选）
REACT_APP_JUHE_API_KEY=your_juhe_api_key_here

# ===========================================
# 房地产数据服务商API密钥
# ===========================================

# 贝壳找房开放平台（如果有合作）
REACT_APP_BEIKE_API_KEY=your_beike_api_key_here

# 58同城开放平台（如果有合作）
REACT_APP_58_API_KEY=your_58_api_key_here

# ===========================================
# 应用配置
# ===========================================

# 应用环境 (development/production)
REACT_APP_ENV=development

# 是否启用真实数据（true/false）
REACT_APP_ENABLE_REAL_DATA=true

# 数据更新频率（分钟）
REACT_APP_DATA_UPDATE_INTERVAL=5

# 是否启用数据缓存
REACT_APP_ENABLE_CACHE=true

# 缓存过期时间（分钟）
REACT_APP_CACHE_EXPIRE_TIME=5

# ===========================================
# 调试和监控配置
# ===========================================

# 是否启用调试模式
REACT_APP_DEBUG_MODE=false

# 是否启用性能监控
REACT_APP_ENABLE_MONITORING=false

# 错误报告服务API密钥（如Sentry）
REACT_APP_SENTRY_DSN=your_sentry_dsn_here

# ===========================================
# 代理服务器配置（用于解决跨域问题）
# ===========================================

# 代理服务器地址（如果需要）
REACT_APP_PROXY_URL=http://localhost:8080/api

# CORS代理服务（如果使用第三方CORS代理）
REACT_APP_CORS_PROXY=https://cors-anywhere.herokuapp.com/

# ===========================================
# 备用数据源配置
# ===========================================

# 备用数据源API地址
REACT_APP_BACKUP_API_URL=https://backup-api.example.com

# 备用数据源API密钥
REACT_APP_BACKUP_API_KEY=your_backup_api_key_here

# ===========================================
# 使用说明
# ===========================================

# 1. 复制此文件为 .env
# 2. 根据需要填入相应的API密钥
# 3. 如果没有某个API密钥，可以留空，系统会使用模拟数据
# 4. 重启应用以使环境变量生效

# 注意事项：
# - .env 文件不应该提交到版本控制系统
# - 生产环境请使用安全的方式管理API密钥
# - 定期更新API密钥以确保安全性
