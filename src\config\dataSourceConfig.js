/**
 * 数据源配置文件
 * 配置各种真实数据源的API端点和认证信息
 */

// 环境变量配置
const config = {
  // 开发环境配置
  development: {
    // 官方数据源
    official: {
      shanghai_housing_bureau: {
        name: '上海市房屋管理局',
        baseUrl: 'https://fgj.sh.gov.cn',
        apiKey: process.env.REACT_APP_SHANGHAI_API_KEY || '',
        endpoints: {
          dailyStats: '/api/v1/daily-statistics',
          monthlyStats: '/api/v1/monthly-statistics',
          listings: '/api/v1/listings',
          transactions: '/api/v1/transactions'
        },
        rateLimit: {
          requestsPerMinute: 60,
          requestsPerHour: 1000
        }
      },
      national_stats: {
        name: '国家统计局',
        baseUrl: 'https://data.stats.gov.cn',
        endpoints: {
          housingData: '/easyquery.htm',
          priceIndex: '/api/price-index'
        }
      }
    },

    // 第三方数据服务
    thirdParty: {
      akshare: {
        name: 'AKShare数据接口',
        baseUrl: 'https://akshare.akfamily.xyz',
        apiKey: process.env.REACT_APP_AKSHARE_API_KEY || '',
        endpoints: {
          housingPrice: '/api/housing/price',
          housingVolume: '/api/housing/volume',
          marketData: '/api/market/shanghai'
        }
      },
      tushare: {
        name: 'Tushare数据接口',
        baseUrl: 'https://api.tushare.pro',
        apiKey: process.env.REACT_APP_TUSHARE_TOKEN || '',
        endpoints: {
          realEstate: '/real_estate',
          priceData: '/price_data'
        }
      }
    },

    // 公开数据源（无需API密钥）
    public: {
      openData: [
        {
          name: '上海开放数据',
          baseUrl: 'https://data.sh.gov.cn',
          endpoints: {
            housing: '/api/housing',
            statistics: '/api/statistics'
          }
        },
        {
          name: '中国房价行情网',
          baseUrl: 'https://www.creprice.cn',
          type: 'web_scraping',
          selectors: {
            price: '.price-value',
            volume: '.volume-data',
            date: '.update-time'
          }
        }
      ]
    },

    // 备用数据源
    backup: {
      lianjia: {
        name: '链家数据',
        baseUrl: 'https://sh.lianjia.com',
        type: 'web_scraping',
        endpoints: {
          marketData: '/fangjia/',
          dealData: '/chengjiao/'
        }
      },
      anjuke: {
        name: '安居客数据',
        baseUrl: 'https://shanghai.anjuke.com',
        type: 'web_scraping',
        endpoints: {
          priceData: '/market/',
          trendData: '/trend/'
        }
      }
    }
  },

  // 生产环境配置
  production: {
    // 生产环境使用更稳定的数据源
    official: {
      shanghai_housing_bureau: {
        name: '上海市房屋管理局',
        baseUrl: 'https://fgj.sh.gov.cn',
        apiKey: process.env.REACT_APP_SHANGHAI_API_KEY || '',
        endpoints: {
          dailyStats: '/api/v1/daily-statistics',
          monthlyStats: '/api/v1/monthly-statistics'
        },
        rateLimit: {
          requestsPerMinute: 30,
          requestsPerHour: 500
        }
      }
    },
    thirdParty: {
      // 生产环境的第三方数据源配置
    }
  }
};

// 获取当前环境配置
const getCurrentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return config[env] || config.development;
};

// 数据源优先级配置
const DATA_SOURCE_PRIORITY = [
  'official.shanghai_housing_bureau',
  'official.national_stats',
  'thirdParty.akshare',
  'thirdParty.tushare',
  'public.openData',
  'backup.lianjia',
  'backup.anjuke'
];

// 数据源可靠性权重
const RELIABILITY_WEIGHTS = {
  official: 1.0,
  thirdParty: 0.8,
  public: 0.6,
  backup: 0.4
};

// API请求配置
const REQUEST_CONFIG = {
  timeout: 10000, // 10秒超时
  retries: 3,     // 重试3次
  retryDelay: 1000, // 重试间隔1秒
  headers: {
    'User-Agent': 'Shanghai Housing Stats App v1.0',
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
};

// 缓存配置
const CACHE_CONFIG = {
  realTimeData: 5 * 60 * 1000,    // 实时数据缓存5分钟
  dailyData: 60 * 60 * 1000,      // 日度数据缓存1小时
  monthlyData: 24 * 60 * 60 * 1000, // 月度数据缓存24小时
  maxCacheSize: 100               // 最大缓存条目数
};

// 数据验证规则
const VALIDATION_RULES = {
  shanghai: {
    avgPrice: { min: 30000, max: 150000, typical: { min: 50000, max: 90000 } },
    volume: { min: 0, max: 1000, typical: { min: 50, max: 300 } },
    listings: { min: 1000, max: 20000, typical: { min: 6000, max: 12000 } },
    area: { min: 0, max: 100000, typical: { min: 5000, max: 25000 } }
  }
};

// 错误处理配置
const ERROR_HANDLING = {
  maxConsecutiveFailures: 5,  // 最大连续失败次数
  fallbackToMockData: true,   // 失败时是否回退到模拟数据
  logErrors: true,            // 是否记录错误日志
  notifyUser: true            // 是否通知用户数据源问题
};

// 导出配置
export {
  getCurrentConfig,
  DATA_SOURCE_PRIORITY,
  RELIABILITY_WEIGHTS,
  REQUEST_CONFIG,
  CACHE_CONFIG,
  VALIDATION_RULES,
  ERROR_HANDLING
};

export default getCurrentConfig();
