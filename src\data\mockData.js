import { format, subDays, subMonths, subYears } from 'date-fns';

/**
 * 生成随机数值，带有趋势和波动
 * @param {number} base - 基础值
 * @param {number} trend - 趋势系数 (-1 到 1)
 * @param {number} volatility - 波动率 (0 到 1)
 * @param {number} index - 时间索引
 * @returns {number} 生成的数值
 */
const generateValue = (base, trend = 0, volatility = 0.1, index = 0) => {
  const trendValue = base * (1 + trend * index * 0.01);
  const randomFactor = 1 + (Math.random() - 0.5) * volatility;
  return Math.round(trendValue * randomFactor);
};

/**
 * 生成日度数据
 * @param {number} days - 天数
 * @returns {Array} 日度数据数组
 */
export const generateDailyData = (days = 365) => {
  const data = [];
  const basePrice = 72500; // 2025年基础均价 72,500元/㎡
  const baseVolume = 195; // 2025年基础成交量 195套/天
  const baseArea = 17000; // 2025年基础成交面积 17,000㎡/天
  const baseListings = 8800; // 2025年基础挂牌量 8,800套
  
  for (let i = 0; i < days; i++) {
    const date = subDays(new Date(), days - 1 - i);
    
    // 添加季节性因素
    const seasonFactor = 1 + 0.2 * Math.sin((i / 365) * 2 * Math.PI);
    
    const avgPrice = generateValue(basePrice, 0.05, 0.08, i) * seasonFactor;
    const volume = generateValue(baseVolume, -0.02, 0.3, i) * seasonFactor;
    const area = generateValue(baseArea, 0.01, 0.25, i) * seasonFactor;
    const listings = generateValue(baseListings, 0.03, 0.15, i);
    
    data.push({
      date: format(date, 'yyyy-MM-dd'),
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000) // 转换为万元
    });
  }
  
  return data;
};

/**
 * 生成月度数据
 * @param {number} months - 月数
 * @returns {Array} 月度数据数组
 */
export const generateMonthlyData = (months = 24) => {
  const data = [];
  const basePrice = 72500; // 2025年基础均价
  const baseVolume = 5850; // 2025年月度基础成交量
  const baseArea = 515000; // 2025年月度基础成交面积
  const baseListings = 8800; // 2025年基础挂牌量
  
  for (let i = 0; i < months; i++) {
    const date = subMonths(new Date(), months - 1 - i);
    
    const avgPrice = generateValue(basePrice, 0.08, 0.12, i);
    const volume = generateValue(baseVolume, -0.03, 0.25, i);
    const area = generateValue(baseArea, 0.02, 0.2, i);
    const listings = generateValue(baseListings, 0.05, 0.18, i);
    
    data.push({
      date: format(date, 'yyyy-MM'),
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000)
    });
  }
  
  return data;
};

/**
 * 生成季度数据
 * @param {number} quarters - 季度数
 * @returns {Array} 季度数据数组
 */
export const generateQuarterlyData = (quarters = 12) => {
  const data = [];
  const basePrice = 65000;
  const baseVolume = 13500; // 季度基础成交量
  const baseArea = 1080000; // 季度基础成交面积
  const baseListings = 8000;
  
  for (let i = 0; i < quarters; i++) {
    const date = subMonths(new Date(), (quarters - 1 - i) * 3);
    const quarter = Math.floor(date.getMonth() / 3) + 1;
    
    const avgPrice = generateValue(basePrice, 0.1, 0.15, i);
    const volume = generateValue(baseVolume, -0.05, 0.3, i);
    const area = generateValue(baseArea, 0.03, 0.25, i);
    const listings = generateValue(baseListings, 0.08, 0.2, i);
    
    data.push({
      date: `${date.getFullYear()}-Q${quarter}`,
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000)
    });
  }
  
  return data;
};

/**
 * 生成年度数据
 * @param {number} years - 年数
 * @returns {Array} 年度数据数组
 */
export const generateYearlyData = (years = 10) => {
  const data = [];
  const basePrice = 65000;
  const baseVolume = 54000; // 年度基础成交量
  const baseArea = 4320000; // 年度基础成交面积
  const baseListings = 8000;
  
  for (let i = 0; i < years; i++) {
    const date = subYears(new Date(), years - 1 - i);
    
    const avgPrice = generateValue(basePrice, 0.12, 0.2, i);
    const volume = generateValue(baseVolume, -0.08, 0.35, i);
    const area = generateValue(baseArea, 0.05, 0.3, i);
    const listings = generateValue(baseListings, 0.1, 0.25, i);
    
    data.push({
      date: format(date, 'yyyy'),
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000)
    });
  }
  
  return data;
};

/**
 * 生成当前统计数据
 * @returns {Object} 当前统计数据
 */
export const generateCurrentStats = () => {
  const dailyData = generateDailyData(30);
  const monthlyData = generateMonthlyData(12);
  const latest = dailyData[dailyData.length - 1];
  const previousDay = dailyData[dailyData.length - 2];
  const lastMonth = monthlyData[monthlyData.length - 2];
  const yearAgo = monthlyData[monthlyData.length - 13] || monthlyData[0];
  
  return {
    avgPrice: {
      current: latest.avgPrice,
      previous: previousDay.avgPrice,
      yearAgo: yearAgo.avgPrice,
      monthOnMonth: ((latest.avgPrice - previousDay.avgPrice) / previousDay.avgPrice) * 100,
      yearOnYear: ((latest.avgPrice - yearAgo.avgPrice) / yearAgo.avgPrice) * 100
    },
    volume: {
      current: latest.volume,
      previous: previousDay.volume,
      yearAgo: yearAgo.volume,
      monthOnMonth: ((latest.volume - previousDay.volume) / previousDay.volume) * 100,
      yearOnYear: ((latest.volume - yearAgo.volume) / yearAgo.volume) * 100
    },
    listings: {
      current: latest.listings,
      previous: previousDay.listings,
      yearAgo: yearAgo.listings,
      monthOnMonth: ((latest.listings - previousDay.listings) / previousDay.listings) * 100,
      yearOnYear: ((latest.listings - yearAgo.listings) / yearAgo.listings) * 100
    },
    cumulativeVolume: {
      current: monthlyData[monthlyData.length - 1].volume,
      previous: lastMonth.volume,
      yearAgo: yearAgo.volume,
      monthOnMonth: ((monthlyData[monthlyData.length - 1].volume - lastMonth.volume) / lastMonth.volume) * 100,
      yearOnYear: ((monthlyData[monthlyData.length - 1].volume - yearAgo.volume) / yearAgo.volume) * 100
    }
  };
};

// 导出所有数据
export const mockData = {
  daily: generateDailyData(),
  monthly: generateMonthlyData(),
  quarterly: generateQuarterlyData(),
  yearly: generateYearlyData(),
  currentStats: generateCurrentStats()
};

export default mockData;
