<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏠</text></svg>" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>上海房地产数据统计 - 真实数据版</title>
    <style>
      /* 加载动画 */
      .loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
      }
      .loading h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
      }
      .loading p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
      }
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      .status {
        margin-top: 1rem;
        padding: 1rem;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">
        <h1>🏠 上海房地产数据统计</h1>
        <p>正在加载真实数据...</p>
        <div class="spinner"></div>
        <div class="status">
          <div>✅ Tushare Token 已配置</div>
          <div>🔄 正在获取最新房地产数据</div>
          <div>📊 数据质量验证中</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
