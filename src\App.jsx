import React, { useState, useEffect } from 'react';
import { Home, TrendingUp, BarChart3, Calendar, Database } from 'lucide-react';
import StatCard from './components/StatCard';
import FilterButtons from './components/FilterButtons';
import DataTable from './components/DataTable';
import MarketHeatIndex from './components/MarketHeatIndex';
import TrendChart from './components/TrendChart';
import DataSourceManager from './components/DataSourceManager';
import statisticsService from './services/statisticsService';
import { formatNumber } from './utils/dataUtils';

function App() {
  const [currentStats, setCurrentStats] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [selectedMetric, setSelectedMetric] = useState('avgPrice');
  const [timeSeriesData, setTimeSeriesData] = useState([]);
  const [heatIndex, setHeatIndex] = useState(null);
  const [loading, setLoading] = useState(true);
  const [dataSourceInfo, setDataSourceInfo] = useState(null);
  const [showDataManager, setShowDataManager] = useState(false);

  const periodOptions = [
    { value: 'daily', label: '日度数据' },
    { value: 'monthly', label: '月度数据' },
    { value: 'quarterly', label: '季度数据' },
    { value: 'yearly', label: '年度数据' }
  ];

  const metricOptions = [
    { value: 'avgPrice', label: '平均价格' },
    { value: 'volume', label: '成交量' },
    { value: 'area', label: '成交面积' },
    { value: 'totalValue', label: '成交金额' }
  ];

  useEffect(() => {
    loadData();
  }, [selectedPeriod]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 获取统计数据（可能是真实数据或模拟数据）
      const stats = await statisticsService.getCurrentStats();
      const timeSeries = statisticsService.getTimeSeriesData(selectedPeriod, 20);
      const heat = statisticsService.getMarketHeatIndex();
      const sourceInfo = statisticsService.getDataSourceInfo();

      setCurrentStats(stats);
      setTimeSeriesData(timeSeries);
      setHeatIndex(heat);
      setDataSourceInfo(sourceInfo);
    } catch (error) {
      console.error('加载数据失败:', error);
      // 如果真实数据加载失败，尝试使用模拟数据
      try {
        statisticsService.setDataSource(false);
        const fallbackStats = await statisticsService.getCurrentStats();
        setCurrentStats(fallbackStats);
      } catch (fallbackError) {
        console.error('备用数据加载也失败:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDataSourceChange = async (useRealData) => {
    setLoading(true);
    try {
      await loadData();
    } finally {
      setLoading(false);
    }
  };

  const getTableColumns = () => {
    const baseColumns = [
      { key: 'date', title: '时间', type: 'text' }
    ];

    switch (selectedPeriod) {
      case 'daily':
        return [
          ...baseColumns,
          { key: 'avgPrice', title: '均价(元/㎡)', type: 'price' },
          { key: 'volume', title: '成交量(套)', type: 'volume' },
          { key: 'area', title: '成交面积(㎡)', type: 'area' },
          { key: 'listings', title: '挂牌量(套)', type: 'volume' },
          { key: 'totalValue', title: '成交金额(万元)', type: 'money' }
        ];
      default:
        return [
          ...baseColumns,
          { key: 'avgPrice', title: '均价(元/㎡)', type: 'price' },
          { key: 'volume', title: '成交量(套)', type: 'volume' },
          { key: 'area', title: '成交面积(㎡)', type: 'area' },
          { key: 'totalValue', title: '成交金额(万元)', type: 'money' }
        ];
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div style={{ textAlign: 'center', padding: '4rem' }}>
          <div style={{ fontSize: '1.2rem', color: '#666' }}>加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <header className="header">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <h1>
              <Home size={32} style={{ marginRight: '1rem' }} />
              上海房地产数据统计
            </h1>
            <p>实时监控上海房地产市场动态，提供全面的数据分析和趋势预测</p>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            {dataSourceInfo && (
              <div style={{
                fontSize: '0.9rem',
                color: dataSourceInfo.useRealData ? '#27ae60' : '#f39c12',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                <Database size={16} />
                {dataSourceInfo.useRealData ? '真实数据' : '模拟数据'}
                {dataSourceInfo.dataQuality && (
                  <span style={{
                    background: dataSourceInfo.dataQuality.score >= 0.7 ? '#27ae60' : '#f39c12',
                    color: 'white',
                    padding: '0.2rem 0.5rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem'
                  }}>
                    {dataSourceInfo.dataQuality.level}
                  </span>
                )}
              </div>
            )}
            <button
              onClick={() => setShowDataManager(!showDataManager)}
              style={{
                padding: '0.5rem 1rem',
                border: '1px solid #3498db',
                background: 'transparent',
                color: '#3498db',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <Database size={16} />
              数据源管理
            </button>
          </div>
        </div>
      </header>

      {/* 关键指标卡片 */}
      <div className="stats-grid">
        <StatCard
          title="最新成交均价"
          value={currentStats?.avgPrice?.current}
          change={currentStats?.avgPrice?.yearOnYear}
          valueType="price"
          changeType="percentage"
          icon={TrendingUp}
          color="blue"
        />
        <StatCard
          title="当月累计成交量"
          value={currentStats?.cumulativeVolume?.current}
          change={currentStats?.cumulativeVolume?.monthOnMonth}
          valueType="volume"
          changeType="percentage"
          icon={BarChart3}
          color="green"
        />
        <StatCard
          title="总挂牌量"
          value={currentStats?.listings?.current}
          change={currentStats?.listings?.yearOnYear}
          valueType="volume"
          changeType="percentage"
          icon={Home}
          color="orange"
        />
        <StatCard
          title="日成交量"
          value={currentStats?.volume?.current}
          change={currentStats?.volume?.monthOnMonth}
          valueType="volume"
          changeType="percentage"
          icon={Calendar}
          color="purple"
        />
      </div>

      {/* 数据源管理 */}
      {showDataManager && (
        <DataSourceManager
          statisticsService={statisticsService}
          onDataSourceChange={handleDataSourceChange}
        />
      )}

      {/* 市场热度指数 */}
      {heatIndex && (
        <MarketHeatIndex heatData={heatIndex} />
      )}

      {/* 时间维度筛选 */}
      <FilterButtons
        options={periodOptions}
        activeFilter={selectedPeriod}
        onFilterChange={setSelectedPeriod}
      />

      {/* 指标筛选 */}
      <FilterButtons
        options={metricOptions}
        activeFilter={selectedMetric}
        onFilterChange={setSelectedMetric}
        className="mb-4"
      />

      {/* 趋势图表 */}
      <TrendChart
        data={timeSeriesData}
        title={`${periodOptions.find(p => p.value === selectedPeriod)?.label || '数据'}`}
        metric={selectedMetric}
        showComparison={selectedPeriod === 'monthly' || selectedPeriod === 'quarterly'}
      />

      {/* 数据表格 */}
      <DataTable
        title={`${periodOptions.find(p => p.value === selectedPeriod)?.label || '数据'}明细`}
        data={timeSeriesData}
        columns={getTableColumns()}
        maxRows={15}
      />
    </div>
  );
}

export default App;
