import {
  calculateYearOnYear,
  calculateMonthOnMonth,
  aggregateDataByPeriod,
  calculateCumulative,
  filterDataByDateRange,
  calculateSummary
} from '../utils/dataUtils.js';
import { mockData } from '../data/mockData.js';
import realDataService from './realDataService.js';
import dataValidationService from './dataValidationService.js';

/**
 * 统计服务类 - 集成真实数据和模拟数据
 */
class StatisticsService {
  constructor() {
    this.data = mockData;
    // 检查是否配置了真实数据
    this.useRealData = process.env.REACT_APP_ENABLE_REAL_DATA === 'true' &&
                       process.env.REACT_APP_TUSHARE_TOKEN;
    this.dataQuality = null; // 数据质量信息
    this.lastRealDataFetch = null; // 最后一次获取真实数据的时间
    this.realDataCache = null; // 真实数据缓存

    // 如果配置了token，自动启用真实数据
    if (process.env.REACT_APP_TUSHARE_TOKEN) {
      console.log('检测到Tushare Token，启用真实数据模式');
      this.useRealData = true;
    }
  }

  /**
   * 获取当前关键指标
   * @returns {Promise<Object>} 关键指标数据
   */
  async getCurrentStats() {
    if (this.useRealData) {
      try {
        const realData = await this.getRealTimeData();
        return this.calculateCurrentStatsFromRealData(realData);
      } catch (error) {
        console.warn('获取真实数据失败，使用模拟数据:', error.message);
        return this.data.currentStats;
      }
    }
    return this.data.currentStats;
  }

  /**
   * 获取真实数据
   * @returns {Promise<Object>} 真实数据
   */
  async getRealTimeData() {
    // 检查缓存是否有效（5分钟内）
    const now = Date.now();
    if (this.realDataCache &&
        this.lastRealDataFetch &&
        (now - this.lastRealDataFetch) < 5 * 60 * 1000) {
      return this.realDataCache;
    }

    // 获取真实数据
    const rawData = await realDataService.getRealTimeData();

    // 验证数据质量
    const validation = dataValidationService.validateData(rawData);
    this.dataQuality = validation;

    // 如果数据质量不佳，尝试修正
    let finalData = rawData;
    if (!validation.isValid) {
      const correction = dataValidationService.correctData(rawData, validation);
      finalData = correction.data;
      console.warn('数据已修正:', correction.corrections);
    }

    // 缓存数据
    this.realDataCache = finalData;
    this.lastRealDataFetch = now;

    return finalData;
  }

  /**
   * 从真实数据计算当前统计指标
   * @param {Object} realData 真实数据
   * @returns {Object} 统计指标
   */
  calculateCurrentStatsFromRealData(realData) {
    // 获取历史数据用于对比
    const historicalData = this.data.monthly;
    const lastMonth = historicalData[historicalData.length - 2];
    const yearAgo = historicalData[historicalData.length - 13] || historicalData[0];

    return {
      avgPrice: {
        current: realData.avgPrice,
        previous: lastMonth.avgPrice,
        yearAgo: yearAgo.avgPrice,
        monthOnMonth: calculateMonthOnMonth(realData.avgPrice, lastMonth.avgPrice),
        yearOnYear: calculateYearOnYear(realData.avgPrice, yearAgo.avgPrice)
      },
      volume: {
        current: realData.volume,
        previous: lastMonth.volume,
        yearAgo: yearAgo.volume,
        monthOnMonth: calculateMonthOnMonth(realData.volume, lastMonth.volume),
        yearOnYear: calculateYearOnYear(realData.volume, yearAgo.volume)
      },
      listings: {
        current: realData.listings,
        previous: lastMonth.listings,
        yearAgo: yearAgo.listings,
        monthOnMonth: calculateMonthOnMonth(realData.listings, lastMonth.listings),
        yearOnYear: calculateYearOnYear(realData.listings, yearAgo.listings)
      },
      cumulativeVolume: {
        current: realData.volume * 30, // 估算月度累计
        previous: lastMonth.volume,
        yearAgo: yearAgo.volume,
        monthOnMonth: calculateMonthOnMonth(realData.volume * 30, lastMonth.volume),
        yearOnYear: calculateYearOnYear(realData.volume * 30, yearAgo.volume)
      },
      dataQuality: this.dataQuality,
      lastUpdated: realData.lastUpdated,
      source: realData.source || 'real_data'
    };
  }

  /**
   * 获取指定时间维度的数据
   * @param {string} period - 时间维度 ('daily', 'monthly', 'quarterly', 'yearly')
   * @param {number} limit - 限制返回数量
   * @returns {Array} 时间序列数据
   */
  getTimeSeriesData(period = 'daily', limit = null) {
    let data = this.data[period] || [];
    
    if (limit) {
      data = data.slice(-limit);
    }
    
    return data;
  }

  /**
   * 计算指定指标的统计信息
   * @param {string} metric - 指标名称 ('avgPrice', 'volume', 'area', 'listings', 'totalValue')
   * @param {string} period - 时间维度
   * @param {number} periods - 对比期数
   * @returns {Object} 统计信息
   */
  calculateMetricStats(metric, period = 'monthly', periods = 12) {
    const data = this.getTimeSeriesData(period, periods);
    
    if (data.length < 2) {
      return null;
    }

    const current = data[data.length - 1][metric];
    const previous = data[data.length - 2][metric];
    const yearAgo = data[data.length - 13] ? data[data.length - 13][metric] : data[0][metric];

    return {
      current,
      previous,
      yearAgo,
      monthOnMonth: calculateMonthOnMonth(current, previous),
      yearOnYear: calculateYearOnYear(current, yearAgo),
      summary: calculateSummary(data, metric),
      trend: this.calculateTrend(data, metric)
    };
  }

  /**
   * 计算趋势
   * @param {Array} data - 数据数组
   * @param {string} metric - 指标名称
   * @returns {string} 趋势方向 ('up', 'down', 'stable')
   */
  calculateTrend(data, metric) {
    if (data.length < 3) return 'stable';
    
    const recent = data.slice(-3).map(item => item[metric]);
    const slope = (recent[2] - recent[0]) / 2;
    const threshold = recent[1] * 0.02; // 2% 阈值
    
    if (slope > threshold) return 'up';
    if (slope < -threshold) return 'down';
    return 'stable';
  }

  /**
   * 获取累计成交数据
   * @param {string} period - 时间维度
   * @param {string} metric - 累计指标 ('volume', 'area', 'totalValue')
   * @returns {Array} 累计数据
   */
  getCumulativeData(period = 'monthly', metric = 'volume') {
    const data = this.getTimeSeriesData(period);
    return calculateCumulative(data, metric);
  }

  /**
   * 获取同比环比分析数据
   * @param {string} metric - 指标名称
   * @param {string} period - 时间维度
   * @returns {Array} 分析数据
   */
  getComparisonAnalysis(metric, period = 'monthly') {
    const data = this.getTimeSeriesData(period, 24); // 获取24个周期的数据
    
    return data.map((item, index) => {
      const current = item[metric];
      const previousPeriod = index > 0 ? data[index - 1][metric] : null;
      const yearAgo = index >= 12 ? data[index - 12][metric] : null;
      
      return {
        date: item.date,
        value: current,
        monthOnMonth: previousPeriod ? calculateMonthOnMonth(current, previousPeriod) : null,
        yearOnYear: yearAgo ? calculateYearOnYear(current, yearAgo) : null
      };
    });
  }

  /**
   * 获取市场热度指数
   * @returns {Object} 市场热度数据
   */
  getMarketHeatIndex() {
    const dailyData = this.getTimeSeriesData('daily', 30);
    const monthlyData = this.getTimeSeriesData('monthly', 12);
    
    // 计算成交活跃度 (基于成交量)
    const avgVolume = monthlyData.reduce((sum, item) => sum + item.volume, 0) / monthlyData.length;
    const currentVolume = dailyData[dailyData.length - 1].volume * 30; // 估算月度成交量
    const volumeIndex = (currentVolume / avgVolume) * 100;
    
    // 计算价格热度 (基于价格变化)
    const priceChanges = dailyData.slice(-7).map((item, index, arr) => {
      if (index === 0) return 0;
      return calculateMonthOnMonth(item.avgPrice, arr[index - 1].avgPrice);
    });
    const avgPriceChange = priceChanges.reduce((sum, change) => sum + Math.abs(change), 0) / priceChanges.length;
    const priceIndex = Math.min(avgPriceChange * 10, 100);
    
    // 计算供需比
    const currentListings = dailyData[dailyData.length - 1].listings;
    const currentVolume30 = dailyData.slice(-30).reduce((sum, item) => sum + item.volume, 0);
    const supplyDemandRatio = currentListings / (currentVolume30 || 1);
    const supplyIndex = Math.max(0, 100 - supplyDemandRatio);
    
    // 综合热度指数
    const heatIndex = (volumeIndex * 0.4 + priceIndex * 0.3 + supplyIndex * 0.3);
    
    return {
      overall: Math.round(heatIndex),
      volume: Math.round(volumeIndex),
      price: Math.round(priceIndex),
      supply: Math.round(supplyIndex),
      level: this.getHeatLevel(heatIndex)
    };
  }

  /**
   * 获取热度等级
   * @param {number} index - 热度指数
   * @returns {string} 热度等级
   */
  getHeatLevel(index) {
    if (index >= 80) return '火热';
    if (index >= 60) return '活跃';
    if (index >= 40) return '平稳';
    if (index >= 20) return '冷淡';
    return '低迷';
  }

  /**
   * 获取区域对比数据 (模拟)
   * @returns {Array} 区域对比数据
   */
  getDistrictComparison() {
    const districts = ['浦东新区', '徐汇区', '静安区', '黄浦区', '长宁区', '普陀区'];
    const basePrice = 65000;
    
    return districts.map(district => ({
      district,
      avgPrice: Math.round(basePrice * (0.8 + Math.random() * 0.6)),
      volume: Math.round(150 * (0.5 + Math.random() * 1.0)),
      priceChange: (Math.random() - 0.5) * 10,
      volumeChange: (Math.random() - 0.5) * 20
    }));
  }

  /**
   * 获取预测数据 (简单线性预测)
   * @param {string} metric - 指标名称
   * @param {string} period - 时间维度
   * @param {number} forecastPeriods - 预测期数
   * @returns {Array} 预测数据
   */
  getForecastData(metric, period = 'monthly', forecastPeriods = 6) {
    const historicalData = this.getTimeSeriesData(period, 12);

    if (historicalData.length < 3) return [];

    // 简单线性回归预测
    const values = historicalData.map(item => item[metric]);
    const n = values.length;
    const sumX = (n * (n + 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, index) => sum + val * (index + 1), 0);
    const sumX2 = (n * (n + 1) * (2 * n + 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    const forecasts = [];
    for (let i = 1; i <= forecastPeriods; i++) {
      const predictedValue = intercept + slope * (n + i);
      forecasts.push({
        period: n + i,
        value: Math.max(0, Math.round(predictedValue)),
        confidence: Math.max(0.5, 1 - (i * 0.1)) // 置信度随预测期递减
      });
    }

    return forecasts;
  }

  /**
   * 切换数据源
   * @param {boolean} useReal - 是否使用真实数据
   */
  setDataSource(useReal) {
    this.useRealData = useReal;
    if (!useReal) {
      // 清除真实数据缓存
      this.realDataCache = null;
      this.lastRealDataFetch = null;
      this.dataQuality = null;
    }
  }

  /**
   * 获取数据质量信息
   * @returns {Object|null} 数据质量信息
   */
  getDataQuality() {
    return this.dataQuality;
  }

  /**
   * 获取数据源信息
   * @returns {Object} 数据源信息
   */
  getDataSourceInfo() {
    return {
      useRealData: this.useRealData,
      lastUpdate: this.lastRealDataFetch,
      cacheValid: this.realDataCache && this.lastRealDataFetch &&
                   (Date.now() - this.lastRealDataFetch) < 5 * 60 * 1000,
      dataQuality: this.dataQuality ? {
        score: this.dataQuality.qualityScore,
        level: this.dataQuality.qualityScore >= 0.9 ? '优秀' :
               this.dataQuality.qualityScore >= 0.7 ? '良好' :
               this.dataQuality.qualityScore >= 0.5 ? '一般' : '较差'
      } : null
    };
  }

  /**
   * 强制刷新真实数据
   * @returns {Promise<Object>} 刷新后的数据
   */
  async refreshRealData() {
    this.realDataCache = null;
    this.lastRealDataFetch = null;
    return await this.getRealTimeData();
  }

  /**
   * 获取数据可靠性报告
   * @returns {Object} 可靠性报告
   */
  getReliabilityReport() {
    if (!this.dataQuality) {
      return {
        available: false,
        message: '暂无数据质量信息'
      };
    }

    const report = dataValidationService.generateQualityReport(this.dataQuality);

    return {
      available: true,
      ...report,
      dataSource: this.useRealData ? '真实数据' : '模拟数据',
      lastCheck: new Date().toISOString()
    };
  }

  /**
   * 获取数据源状态
   * @returns {Promise<Object>} 数据源状态
   */
  async getDataSourceStatus() {
    const status = {
      realData: {
        available: false,
        latency: null,
        error: null
      },
      mockData: {
        available: true,
        latency: 0
      }
    };

    // 测试真实数据源
    try {
      const startTime = Date.now();
      await realDataService.getRealTimeData();
      status.realData.available = true;
      status.realData.latency = Date.now() - startTime;
    } catch (error) {
      status.realData.error = error.message;
    }

    return status;
  }
}

// 创建单例实例
const statisticsService = new StatisticsService();

export default statisticsService;
