const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 8080; // 改用8080端口

const server = http.createServer((req, res) => {
  console.log(`${req.method} ${req.url}`);

  // 处理根路径
  let filePath = req.url;
  if (filePath === '/') {
    filePath = '/test.html';
  }

  const fullPath = path.join(__dirname, filePath);

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  fs.readFile(fullPath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end('<h1>404 - 文件未找到</h1><p>请确保文件存在</p>', 'utf-8');
      } else {
        res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`<h1>服务器错误</h1><p>${error.message}</p>`, 'utf-8');
      }
    } else {
      // 根据文件扩展名设置Content-Type
      let contentType = 'text/html; charset=utf-8';
      if (fullPath.endsWith('.css')) {
        contentType = 'text/css';
      } else if (fullPath.endsWith('.js')) {
        contentType = 'application/javascript';
      }

      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
});

server.listen(port, '127.0.0.1', () => {
  console.log(`🏠 上海房地产数据统计小程序`);
  console.log(`🚀 服务器运行在: http://localhost:${port}`);
  console.log(`📊 访问演示页面: http://localhost:${port}/test.html`);
  console.log(`⏹️  按 Ctrl+C 停止服务器`);
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`端口 ${port} 已被占用，请尝试其他端口`);
  } else {
    console.error('服务器启动失败:', err.message);
  }
});
