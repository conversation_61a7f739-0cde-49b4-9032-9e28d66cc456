import React, { useState, useEffect } from 'react';
import { Database, Wifi, WifiOff, RefreshCw, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

const DataSourceManager = ({ statisticsService, onDataSourceChange }) => {
  const [dataSourceInfo, setDataSourceInfo] = useState(null);
  const [dataSourceStatus, setDataSourceStatus] = useState(null);
  const [reliabilityReport, setReliabilityReport] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDataSourceInfo();
    checkDataSourceStatus();
  }, []);

  const loadDataSourceInfo = async () => {
    try {
      const info = statisticsService.getDataSourceInfo();
      const report = statisticsService.getReliabilityReport();
      
      setDataSourceInfo(info);
      setReliabilityReport(report);
    } catch (error) {
      console.error('加载数据源信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkDataSourceStatus = async () => {
    try {
      const status = await statisticsService.getDataSourceStatus();
      setDataSourceStatus(status);
    } catch (error) {
      console.error('检查数据源状态失败:', error);
    }
  };

  const handleDataSourceToggle = async (useRealData) => {
    try {
      statisticsService.setDataSource(useRealData);
      await loadDataSourceInfo();
      if (onDataSourceChange) {
        onDataSourceChange(useRealData);
      }
    } catch (error) {
      console.error('切换数据源失败:', error);
    }
  };

  const handleRefreshData = async () => {
    setIsRefreshing(true);
    try {
      await statisticsService.refreshRealData();
      await loadDataSourceInfo();
      await checkDataSourceStatus();
      if (onDataSourceChange) {
        onDataSourceChange(dataSourceInfo?.useRealData);
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getQualityColor = (score) => {
    if (score >= 90) return '#27ae60';
    if (score >= 70) return '#f39c12';
    if (score >= 50) return '#e67e22';
    return '#e74c3c';
  };

  const getStatusIcon = (available, error) => {
    if (available) return <CheckCircle size={16} className="text-green-500" />;
    if (error) return <AlertTriangle size={16} className="text-red-500" />;
    return <WifiOff size={16} className="text-gray-400" />;
  };

  if (loading) {
    return (
      <div className="chart-container">
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div style={{ fontSize: '1rem', color: '#666' }}>加载数据源信息...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="chart-container">
      <h3 className="chart-title">
        <Database size={20} style={{ marginRight: '0.5rem' }} />
        数据源管理
      </h3>

      {/* 数据源切换 */}
      <div style={{ marginBottom: '1.5rem' }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '1rem',
          marginBottom: '1rem'
        }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <input
              type="radio"
              name="dataSource"
              checked={dataSourceInfo?.useRealData === true}
              onChange={() => handleDataSourceToggle(true)}
            />
            <Wifi size={16} />
            真实数据
          </label>
          <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <input
              type="radio"
              name="dataSource"
              checked={dataSourceInfo?.useRealData === false}
              onChange={() => handleDataSourceToggle(false)}
            />
            <Database size={16} />
            模拟数据
          </label>
        </div>

        <button
          onClick={handleRefreshData}
          disabled={isRefreshing || !dataSourceInfo?.useRealData}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.5rem 1rem',
            border: '1px solid #3498db',
            background: isRefreshing ? '#ecf0f1' : 'transparent',
            color: isRefreshing ? '#666' : '#3498db',
            borderRadius: '4px',
            cursor: isRefreshing || !dataSourceInfo?.useRealData ? 'not-allowed' : 'pointer'
          }}
        >
          <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
          {isRefreshing ? '刷新中...' : '刷新数据'}
        </button>
      </div>

      {/* 数据源状态 */}
      {dataSourceStatus && (
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{ marginBottom: '0.5rem', color: '#2c3e50' }}>数据源状态</h4>
          <div style={{ display: 'grid', gap: '0.5rem' }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              padding: '0.5rem',
              background: '#f8f9fa',
              borderRadius: '4px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                {getStatusIcon(dataSourceStatus.realData.available, dataSourceStatus.realData.error)}
                <span>真实数据源</span>
              </div>
              <div style={{ fontSize: '0.9rem', color: '#666' }}>
                {dataSourceStatus.realData.available 
                  ? `${dataSourceStatus.realData.latency}ms`
                  : dataSourceStatus.realData.error || '不可用'
                }
              </div>
            </div>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              padding: '0.5rem',
              background: '#f8f9fa',
              borderRadius: '4px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <CheckCircle size={16} className="text-green-500" />
                <span>模拟数据源</span>
              </div>
              <div style={{ fontSize: '0.9rem', color: '#666' }}>
                可用
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 数据质量报告 */}
      {reliabilityReport?.available && (
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{ marginBottom: '0.5rem', color: '#2c3e50' }}>数据质量报告</h4>
          <div style={{ 
            padding: '1rem',
            background: '#f8f9fa',
            borderRadius: '8px',
            border: `3px solid ${getQualityColor(reliabilityReport.qualityScore)}`
          }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              marginBottom: '0.5rem'
            }}>
              <span style={{ fontWeight: '600' }}>总体质量</span>
              <span style={{ 
                color: getQualityColor(reliabilityReport.qualityScore),
                fontWeight: 'bold'
              }}>
                {reliabilityReport.qualityLevel} ({reliabilityReport.qualityScore}%)
              </span>
            </div>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.5rem', fontSize: '0.9rem' }}>
              <div>完整性: {reliabilityReport.summary.completeness}%</div>
              <div>准确性: {reliabilityReport.summary.accuracy}%</div>
              <div>时效性: {reliabilityReport.summary.timeliness}%</div>
              <div>一致性: {reliabilityReport.summary.consistency}%</div>
            </div>

            {reliabilityReport.recommendations?.length > 0 && (
              <div style={{ marginTop: '0.5rem' }}>
                <div style={{ fontSize: '0.9rem', fontWeight: '600', marginBottom: '0.25rem' }}>
                  改进建议:
                </div>
                <ul style={{ fontSize: '0.8rem', color: '#666', paddingLeft: '1rem' }}>
                  {reliabilityReport.recommendations.map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 缓存信息 */}
      {dataSourceInfo && (
        <div>
          <h4 style={{ marginBottom: '0.5rem', color: '#2c3e50' }}>缓存信息</h4>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '0.5rem',
            fontSize: '0.9rem',
            color: '#666'
          }}>
            <Clock size={14} />
            <span>
              {dataSourceInfo.cacheValid 
                ? `缓存有效 (${Math.round((Date.now() - dataSourceInfo.lastUpdate) / 1000)}秒前更新)`
                : '缓存已过期'
              }
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataSourceManager;
