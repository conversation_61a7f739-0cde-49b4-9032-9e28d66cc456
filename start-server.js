const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html; charset=utf-8',
  '.js': 'application/javascript; charset=utf-8',
  '.css': 'text/css; charset=utf-8',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  let filePath = req.url;
  
  // 处理根路径
  if (filePath === '/') {
    filePath = '/index.html';
  }

  // 处理SPA路由
  if (!path.extname(filePath) && filePath !== '/index.html') {
    filePath = '/index.html';
  }

  const fullPath = path.join(__dirname, filePath);
  const ext = path.extname(fullPath).toLowerCase();
  const contentType = mimeTypes[ext] || 'application/octet-stream';

  // 检查文件是否存在
  fs.access(fullPath, fs.constants.F_OK, (err) => {
    if (err) {
      // 如果文件不存在，返回index.html（用于SPA）
      const indexPath = path.join(__dirname, 'index.html');
      fs.readFile(indexPath, (indexErr, indexContent) => {
        if (indexErr) {
          res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
          res.end(`
            <html>
              <head><title>404 - 页面未找到</title></head>
              <body>
                <h1>404 - 页面未找到</h1>
                <p>请求的文件: ${filePath}</p>
                <p><a href="/">返回首页</a></p>
              </body>
            </html>
          `);
        } else {
          res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
          res.end(indexContent);
        }
      });
      return;
    }

    // 读取文件
    fs.readFile(fullPath, (readErr, content) => {
      if (readErr) {
        res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
          <html>
            <head><title>500 - 服务器错误</title></head>
            <body>
              <h1>500 - 服务器错误</h1>
              <p>错误信息: ${readErr.message}</p>
            </body>
          </html>
        `);
      } else {
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(content);
      }
    });
  });
});

server.listen(PORT, () => {
  console.log('='.repeat(50));
  console.log('🏠 上海房地产数据统计小程序');
  console.log('='.repeat(50));
  console.log(`🚀 服务器已启动！`);
  console.log(`📍 本地访问地址: http://localhost:${PORT}`);
  console.log(`🌐 网络访问地址: http://127.0.0.1:${PORT}`);
  console.log(`📊 真实数据已启用 (Tushare)`);
  console.log('='.repeat(50));
  console.log('💡 提示:');
  console.log('   - 按 Ctrl+C 停止服务器');
  console.log('   - 在浏览器中打开上述地址访问应用');
  console.log('   - 点击右上角"数据源管理"查看数据状态');
  console.log('='.repeat(50));
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
    console.log('💡 解决方案:');
    console.log('   1. 关闭占用端口的程序');
    console.log('   2. 或修改 start-server.js 中的 PORT 变量');
  } else {
    console.error('❌ 服务器启动失败:', err.message);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
