<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海房地产数据统计 - 测试版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3498db;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card h3 {
            color: #2c3e50;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .stat-change.positive {
            color: #27ae60;
        }

        .stat-change.negative {
            color: #e74c3c;
        }

        .demo-table {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }

        .demo-table h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status {
            background: #e8f5e8;
            color: #27ae60;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin-top: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🏠 上海房地产数据统计</h1>
            <p>实时监控上海房地产市场动态，提供全面的数据分析和趋势预测</p>
        </header>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>最新成交均价</h3>
                <div class="stat-value">¥67,234</div>
                <div class="stat-change positive">↗ +3.2%</div>
            </div>
            <div class="stat-card">
                <h3>当月累计成交量</h3>
                <div class="stat-value">4,567套</div>
                <div class="stat-change negative">↘ -1.8%</div>
            </div>
            <div class="stat-card">
                <h3>总挂牌量</h3>
                <div class="stat-value">8,234套</div>
                <div class="stat-change positive">↗ +5.6%</div>
            </div>
            <div class="stat-card">
                <h3>日成交量</h3>
                <div class="stat-value">156套</div>
                <div class="stat-change positive">↗ +2.1%</div>
            </div>
        </div>

        <div class="demo-table">
            <h3>📊 最近数据明细</h3>
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>均价(元/㎡)</th>
                        <th>成交量(套)</th>
                        <th>成交面积(㎡)</th>
                        <th>成交金额(万元)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024-07-08</td>
                        <td>¥67,234</td>
                        <td>156套</td>
                        <td>12,456㎡</td>
                        <td>8,372万</td>
                    </tr>
                    <tr>
                        <td>2024-07-07</td>
                        <td>¥66,890</td>
                        <td>142套</td>
                        <td>11,234㎡</td>
                        <td>7,512万</td>
                    </tr>
                    <tr>
                        <td>2024-07-06</td>
                        <td>¥67,456</td>
                        <td>178套</td>
                        <td>14,567㎡</td>
                        <td>9,823万</td>
                    </tr>
                    <tr>
                        <td>2024-07-05</td>
                        <td>¥66,123</td>
                        <td>134套</td>
                        <td>10,890㎡</td>
                        <td>7,201万</td>
                    </tr>
                    <tr>
                        <td>2024-07-04</td>
                        <td>¥68,012</td>
                        <td>189套</td>
                        <td>15,234㎡</td>
                        <td>10,356万</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="status">
            ✅ 上海房地产数据统计小程序已成功创建！包含最新成交均价、当月累计成交量、总挂牌量、每天明细数据等功能。
        </div>
    </div>
</body>
</html>
