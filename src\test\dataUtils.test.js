import { describe, it, expect } from 'vitest';
import { 
  calculateYearOnYear, 
  calculateMonthOnMonth, 
  formatNumber,
  aggregateDataByPeriod 
} from '../utils/dataUtils.js';

describe('数据工具函数测试', () => {
  it('应该正确计算同比变化率', () => {
    expect(calculateYearOnYear(110, 100)).toBe(10);
    expect(calculateYearOnYear(90, 100)).toBe(-10);
    expect(calculateYearOnYear(100, 0)).toBe(0);
  });

  it('应该正确计算环比变化率', () => {
    expect(calculateMonthOnMonth(105, 100)).toBe(5);
    expect(calculateMonthOnMonth(95, 100)).toBe(-5);
    expect(calculateMonthOnMonth(100, 0)).toBe(0);
  });

  it('应该正确格式化数字', () => {
    expect(formatNumber(65000, 'price')).toBe('¥65,000');
    expect(formatNumber(150, 'volume')).toBe('150套');
    expect(formatNumber(12000, 'area')).toBe('12,000㎡');
    expect(formatNumber(5.5, 'percentage')).toBe('+5.50%');
    expect(formatNumber(-2.3, 'percentage')).toBe('-2.30%');
  });

  it('应该正确聚合数据', () => {
    const testData = [
      { date: '2024-01-01', avgPrice: 60000, volume: 100 },
      { date: '2024-01-02', avgPrice: 62000, volume: 120 },
      { date: '2024-02-01', avgPrice: 64000, volume: 110 }
    ];

    const monthlyData = aggregateDataByPeriod(testData, 'monthly');
    expect(monthlyData).toHaveLength(2);
    expect(monthlyData[0].date).toBe('2024-01');
    expect(monthlyData[0].volume).toBe(220); // 100 + 120
  });
});
