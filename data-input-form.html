<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>官方数据输入 - fangdi.com.cn真实数据</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 15px;
        }

        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .instructions {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .instructions h3 {
            color: #27ae60;
            margin-bottom: 1rem;
        }

        .form-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group .unit {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .data-source {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .data-source h3 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .submit-section {
            text-align: center;
            margin-top: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(243, 156, 18, 0.3);
        }

        .preview-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            display: none;
        }

        .preview-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .preview-data {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 官方数据输入</h1>
            <p>请从fangdi.com.cn获取真实数据并输入到下方表单</p>
        </div>

        <div class="instructions">
            <h3>📋 数据获取指南</h3>
            <ol style="padding-left: 2rem;">
                <li>打开 <a href="https://www.fangdi.com.cn/" target="_blank" style="color: #3498db;">https://www.fangdi.com.cn/</a></li>
                <li>查看首页的最新成交数据</li>
                <li>记录二手房和新房的关键指标</li>
                <li>将真实数据填入下方表单</li>
                <li>点击"更新数据"应用到系统中</li>
            </ol>
        </div>

        <div class="data-source">
            <h3>🔗 数据来源确认</h3>
            <p>数据来源：<strong>fangdi.com.cn</strong> (上海房地产交易中心官方网站)</p>
            <p>更新时间：<span id="updateTime"></span></p>
        </div>

        <form id="dataForm">
            <!-- 二手房数据 -->
            <div class="form-section">
                <h3>🏠 二手房数据 (从fangdi.com.cn获取)</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="secondHandPrice">最新成交均价</label>
                        <input type="number" id="secondHandPrice" name="secondHandPrice" placeholder="例如: 31600">
                        <div class="unit">单位: 元/平方米</div>
                    </div>
                    <div class="form-group">
                        <label for="secondHandDaily">昨日成交量</label>
                        <input type="number" id="secondHandDaily" name="secondHandDaily" placeholder="例如: 492">
                        <div class="unit">单位: 套</div>
                    </div>
                    <div class="form-group">
                        <label for="secondHandMonthly">当月累计成交</label>
                        <input type="number" id="secondHandMonthly" name="secondHandMonthly" placeholder="例如: 4852">
                        <div class="unit">单位: 套</div>
                    </div>
                    <div class="form-group">
                        <label for="secondHandListings">总挂牌量</label>
                        <input type="number" id="secondHandListings" name="secondHandListings" placeholder="例如: 344855">
                        <div class="unit">单位: 笔</div>
                    </div>
                    <div class="form-group">
                        <label for="secondHandArea">昨日成交面积</label>
                        <input type="number" id="secondHandArea" name="secondHandArea" placeholder="例如: 46457" step="0.01">
                        <div class="unit">单位: 平方米</div>
                    </div>
                </div>
            </div>

            <!-- 新房数据 -->
            <div class="form-section">
                <h3>🏢 新房数据 (从fangdi.com.cn获取)</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="newHousePrice">最新成交均价</label>
                        <input type="number" id="newHousePrice" name="newHousePrice" placeholder="例如: 45000">
                        <div class="unit">单位: 元/平方米</div>
                    </div>
                    <div class="form-group">
                        <label for="newHouseDaily">昨日成交量</label>
                        <input type="number" id="newHouseDaily" name="newHouseDaily" placeholder="例如: 303">
                        <div class="unit">单位: 套</div>
                    </div>
                    <div class="form-group">
                        <label for="newHouseMonthly">当月累计成交</label>
                        <input type="number" id="newHouseMonthly" name="newHouseMonthly" placeholder="例如: 2121">
                        <div class="unit">单位: 套</div>
                    </div>
                    <div class="form-group">
                        <label for="newHouseListings">总挂牌量</label>
                        <input type="number" id="newHouseListings" name="newHouseListings" placeholder="例如: 15000">
                        <div class="unit">单位: 套</div>
                    </div>
                    <div class="form-group">
                        <label for="newHouseArea">昨日成交面积</label>
                        <input type="number" id="newHouseArea" name="newHouseArea" placeholder="例如: 22400" step="0.01">
                        <div class="unit">单位: 平方米</div>
                    </div>
                </div>
            </div>

            <!-- 数据确认 -->
            <div class="form-section">
                <h3>✅ 数据确认</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="dataDate">数据日期</label>
                        <input type="date" id="dataDate" name="dataDate">
                    </div>
                    <div class="form-group">
                        <label for="dataTime">数据时间</label>
                        <input type="time" id="dataTime" name="dataTime">
                    </div>
                </div>
            </div>
        </form>

        <div class="submit-section">
            <button type="button" class="btn btn-warning" onclick="openOfficialSite()">访问官方网站</button>
            <button type="button" class="btn btn-primary" onclick="previewData()">预览数据</button>
            <button type="button" class="btn btn-success" onclick="updateData()">更新系统数据</button>
        </div>

        <div class="preview-section" id="previewSection">
            <h3>📊 数据预览</h3>
            <div class="preview-data" id="previewData"></div>
        </div>
    </div>

    <script>
        // 设置当前时间
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            document.getElementById('dataDate').value = now.toISOString().split('T')[0];
            document.getElementById('dataTime').value = now.toTimeString().split(' ')[0].substring(0, 5);
            document.getElementById('updateTime').textContent = now.toLocaleString('zh-CN');
        });

        function openOfficialSite() {
            window.open('https://www.fangdi.com.cn/', '_blank');
        }

        function previewData() {
            const formData = new FormData(document.getElementById('dataForm'));
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            const structuredData = {
                date: data.dataDate,
                time: data.dataTime,
                secondHand: {
                    avgPrice: parseInt(data.secondHandPrice) || 0,
                    dailyVolume: parseInt(data.secondHandDaily) || 0,
                    monthlyVolume: parseInt(data.secondHandMonthly) || 0,
                    listings: parseInt(data.secondHandListings) || 0,
                    area: parseFloat(data.secondHandArea) || 0
                },
                newHouse: {
                    avgPrice: parseInt(data.newHousePrice) || 0,
                    dailyVolume: parseInt(data.newHouseDaily) || 0,
                    monthlyVolume: parseInt(data.newHouseMonthly) || 0,
                    listings: parseInt(data.newHouseListings) || 0,
                    area: parseFloat(data.newHouseArea) || 0
                },
                dataSource: "fangdi.com.cn",
                updateTime: new Date().toISOString()
            };

            document.getElementById('previewData').textContent = JSON.stringify(structuredData, null, 2);
            document.getElementById('previewSection').style.display = 'block';
        }

        function updateData() {
            const formData = new FormData(document.getElementById('dataForm'));
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // 验证必填字段
            const requiredFields = ['secondHandPrice', 'secondHandDaily', 'secondHandMonthly', 'secondHandListings'];
            const missingFields = requiredFields.filter(field => !data[field]);
            
            if (missingFields.length > 0) {
                alert('请填写以下必填字段:\n' + missingFields.join('\n'));
                return;
            }

            // 保存数据到localStorage
            const structuredData = {
                date: data.dataDate,
                time: data.dataTime,
                secondHand: {
                    avgPrice: parseInt(data.secondHandPrice),
                    dailyVolume: parseInt(data.secondHandDaily),
                    monthlyVolume: parseInt(data.secondHandMonthly),
                    listings: parseInt(data.secondHandListings),
                    area: parseFloat(data.secondHandArea) || 0
                },
                newHouse: {
                    avgPrice: parseInt(data.newHousePrice) || 0,
                    dailyVolume: parseInt(data.newHouseDaily) || 0,
                    monthlyVolume: parseInt(data.newHouseMonthly) || 0,
                    listings: parseInt(data.newHouseListings) || 0,
                    area: parseFloat(data.newHouseArea) || 0
                },
                dataSource: "fangdi.com.cn",
                updateTime: new Date().toISOString()
            };

            localStorage.setItem('officialRealEstateData', JSON.stringify(structuredData));
            
            alert('✅ 数据更新成功！\n\n基于fangdi.com.cn官方数据:\n' +
                  `• 二手房均价: ¥${structuredData.secondHand.avgPrice.toLocaleString()}/㎡\n` +
                  `• 二手房日成交: ${structuredData.secondHand.dailyVolume}套\n` +
                  `• 二手房月累计: ${structuredData.secondHand.monthlyVolume}套\n` +
                  `• 二手房挂牌量: ${structuredData.secondHand.listings.toLocaleString()}笔\n\n` +
                  '现在可以启动React应用查看更新后的数据！');
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('📊 官方数据输入表单加载完成');
            console.log('🔗 请访问 https://www.fangdi.com.cn/ 获取最新数据');
            console.log('📋 填写表单后点击"更新系统数据"');
            console.log('✅ 确保所有数据都来自官方网站');
        });
    </script>
</body>
</html>
