@echo off
echo ========================================
echo 上海房地产数据统计小程序
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js环境检查通过
echo.

echo 正在安装依赖包...
npm install
if %errorlevel% neq 0 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 依赖包安装完成
echo.

echo 正在启动开发服务器...
echo 请稍等，服务器启动后会自动打开浏览器
echo.
echo 如果浏览器没有自动打开，请手动访问: http://localhost:3000
echo.
echo 按 Ctrl+C 可以停止服务器
echo.

npm run dev

pause
