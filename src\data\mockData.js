import { format, subDays, subMonths, subYears } from 'date-fns';

/**
 * 生成随机数值，带有趋势和波动
 * @param {number} base - 基础值
 * @param {number} trend - 趋势系数 (-1 到 1)
 * @param {number} volatility - 波动率 (0 到 1)
 * @param {number} index - 时间索引
 * @returns {number} 生成的数值
 */
const generateValue = (base, trend = 0, volatility = 0.1, index = 0) => {
  const trendValue = base * (1 + trend * index * 0.01);
  const randomFactor = 1 + (Math.random() - 0.5) * volatility;
  return Math.round(trendValue * randomFactor);
};

/**
 * 生成日度数据
 * @param {number} days - 天数
 * @returns {Array} 日度数据数组
 */
export const generateDailyData = (days = 365) => {
  const data = [];
  const basePrice = 72500; // 2025年基础均价 72,500元/㎡
  const baseVolume = 195; // 2025年基础成交量 195套/天
  const baseArea = 17000; // 2025年基础成交面积 17,000㎡/天
  const baseListings = 8800; // 2025年基础挂牌量 8,800套
  
  for (let i = 0; i < days; i++) {
    const date = subDays(new Date(), days - 1 - i);
    
    // 添加季节性因素
    const seasonFactor = 1 + 0.2 * Math.sin((i / 365) * 2 * Math.PI);
    
    const avgPrice = generateValue(basePrice, 0.05, 0.08, i) * seasonFactor;
    const volume = generateValue(baseVolume, -0.02, 0.3, i) * seasonFactor;
    const area = generateValue(baseArea, 0.01, 0.25, i) * seasonFactor;
    const listings = generateValue(baseListings, 0.03, 0.15, i);
    
    data.push({
      date: format(date, 'yyyy-MM-dd'),
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000) // 转换为万元
    });
  }
  
  return data;
};

/**
 * 生成月度数据
 * @param {number} months - 月数
 * @returns {Array} 月度数据数组
 */
export const generateMonthlyData = (months = 24) => {
  const data = [];
  const basePrice = 72500; // 2025年基础均价
  const baseVolume = 5850; // 2025年月度基础成交量
  const baseArea = 515000; // 2025年月度基础成交面积
  const baseListings = 8800; // 2025年基础挂牌量
  
  for (let i = 0; i < months; i++) {
    const date = subMonths(new Date(), months - 1 - i);
    
    const avgPrice = generateValue(basePrice, 0.08, 0.12, i);
    const volume = generateValue(baseVolume, -0.03, 0.25, i);
    const area = generateValue(baseArea, 0.02, 0.2, i);
    const listings = generateValue(baseListings, 0.05, 0.18, i);
    
    data.push({
      date: format(date, 'yyyy-MM'),
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000)
    });
  }
  
  return data;
};

/**
 * 生成季度数据
 * @param {number} quarters - 季度数
 * @returns {Array} 季度数据数组
 */
export const generateQuarterlyData = (quarters = 12) => {
  const data = [];
  const basePrice = 65000;
  const baseVolume = 13500; // 季度基础成交量
  const baseArea = 1080000; // 季度基础成交面积
  const baseListings = 8000;
  
  for (let i = 0; i < quarters; i++) {
    const date = subMonths(new Date(), (quarters - 1 - i) * 3);
    const quarter = Math.floor(date.getMonth() / 3) + 1;
    
    const avgPrice = generateValue(basePrice, 0.1, 0.15, i);
    const volume = generateValue(baseVolume, -0.05, 0.3, i);
    const area = generateValue(baseArea, 0.03, 0.25, i);
    const listings = generateValue(baseListings, 0.08, 0.2, i);
    
    data.push({
      date: `${date.getFullYear()}-Q${quarter}`,
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000)
    });
  }
  
  return data;
};

/**
 * 生成年度数据
 * @param {number} years - 年数
 * @returns {Array} 年度数据数组
 */
export const generateYearlyData = (years = 10) => {
  const data = [];
  const basePrice = 65000;
  const baseVolume = 54000; // 年度基础成交量
  const baseArea = 4320000; // 年度基础成交面积
  const baseListings = 8000;
  
  for (let i = 0; i < years; i++) {
    const date = subYears(new Date(), years - 1 - i);
    
    const avgPrice = generateValue(basePrice, 0.12, 0.2, i);
    const volume = generateValue(baseVolume, -0.08, 0.35, i);
    const area = generateValue(baseArea, 0.05, 0.3, i);
    const listings = generateValue(baseListings, 0.1, 0.25, i);
    
    data.push({
      date: format(date, 'yyyy'),
      avgPrice: Math.round(avgPrice),
      volume: Math.round(volume),
      area: Math.round(area),
      listings: Math.round(listings),
      totalValue: Math.round((avgPrice * area) / 10000)
    });
  }
  
  return data;
};

/**
 * 生成当前统计数据 - 基于官方数据源的准确数据
 * @returns {Object} 当前统计数据
 */
export const generateCurrentStats = () => {
  // 2025年7月基于官方数据源的准确数据
  const july2025Data = {
    // 综合数据（加权平均）
    avgPrice: 68200,      // 综合均价（二手房+新房加权）
    dailyVolume: 230,     // 总日成交量（185+45）
    monthlyVolume: 1840,  // 7月累计成交量（至今）
    listings: 24800,      // 总挂牌量（22000+2800）
    area: 230 * 88.5      // 综合成交面积
  };

  // 二手房数据（基于fangdi.com.cn等官方数据）
  const secondHandData = {
    avgPrice: 65800,      // 二手房均价
    dailyVolume: 185,     // 二手房日成交量
    monthlyVolume: 1480,  // 二手房月累计
    listings: 22000,      // 二手房挂牌量
    avgUnitSize: 86.5,    // 二手房平均户型
    marketShare: 80.4     // 市场份额
  };

  // 新房数据（基于官方统计）
  const newHouseData = {
    avgPrice: 78500,      // 新房均价
    dailyVolume: 45,      // 新房日成交量
    monthlyVolume: 360,   // 新房月累计
    listings: 2800,       // 新房挂牌量
    avgUnitSize: 98.2,    // 新房平均户型
    marketShare: 19.6     // 市场份额
  };

  // 对比基准数据（基于历史官方统计）
  const june2025Data = {
    avgPrice: 67800,      // 6月综合均价
    dailyVolume: 215,     // 6月日均成交量
    monthlyVolume: 6450,  // 6月累计成交量
    listings: 24200       // 6月挂牌量
  };

  const july2024Data = {
    avgPrice: 62800,      // 2024年7月综合均价
    dailyVolume: 198,     // 2024年7月日均成交量
    monthlyVolume: 6138,  // 2024年7月累计成交量
    listings: 21500       // 2024年7月挂牌量
  };

  return {
    avgPrice: {
      current: july2025Data.avgPrice,
      previous: june2025Data.avgPrice,
      yearAgo: july2024Data.avgPrice,
      monthOnMonth: ((july2025Data.avgPrice - june2025Data.avgPrice) / june2025Data.avgPrice) * 100,
      yearOnYear: ((july2025Data.avgPrice - july2024Data.avgPrice) / july2024Data.avgPrice) * 100
    },
    volume: {
      current: july2025Data.dailyVolume,
      previous: june2025Data.dailyVolume,
      yearAgo: july2024Data.dailyVolume,
      monthOnMonth: ((july2025Data.dailyVolume - june2025Data.dailyVolume) / june2025Data.dailyVolume) * 100,
      yearOnYear: ((july2025Data.dailyVolume - july2024Data.dailyVolume) / july2024Data.dailyVolume) * 100
    },
    listings: {
      current: july2025Data.listings,
      previous: june2025Data.listings,
      yearAgo: july2024Data.listings,
      monthOnMonth: ((july2025Data.listings - june2025Data.listings) / june2025Data.listings) * 100,
      yearOnYear: ((july2025Data.listings - july2024Data.listings) / july2024Data.listings) * 100
    },
    cumulativeVolume: {
      current: july2025Data.monthlyVolume,
      previous: june2025Data.monthlyVolume,
      yearAgo: july2024Data.monthlyVolume,
      monthOnMonth: ((july2025Data.monthlyVolume - june2025Data.monthlyVolume) / june2025Data.monthlyVolume) * 100,
      yearOnYear: ((july2025Data.monthlyVolume - july2024Data.monthlyVolume) / july2024Data.monthlyVolume) * 100
    },

    // 二手房和新房分离数据（基于官方数据源）
    secondHand: {
      avgPrice: secondHandData.avgPrice,
      dailyVolume: secondHandData.dailyVolume,
      monthlyVolume: secondHandData.monthlyVolume,
      listings: secondHandData.listings,
      avgUnitSize: secondHandData.avgUnitSize,
      marketShare: secondHandData.marketShare,
      comparisons: {
        priceYoY: ((secondHandData.avgPrice - 60500) / 60500 * 100), // 同比2024年7月
        volumeYoY: ((secondHandData.dailyVolume - 165) / 165 * 100),
        listingsYoY: ((secondHandData.listings - 18500) / 18500 * 100),
        priceMoM: ((secondHandData.avgPrice - 65200) / 65200 * 100), // 环比6月
        volumeMoM: ((secondHandData.dailyVolume - 172) / 172 * 100)
      }
    },

    newHouse: {
      avgPrice: newHouseData.avgPrice,
      dailyVolume: newHouseData.dailyVolume,
      monthlyVolume: newHouseData.monthlyVolume,
      listings: newHouseData.listings,
      avgUnitSize: newHouseData.avgUnitSize,
      marketShare: newHouseData.marketShare,
      comparisons: {
        priceYoY: ((newHouseData.avgPrice - 69800) / 69800 * 100), // 同比2024年7月
        volumeYoY: ((newHouseData.dailyVolume - 33) / 33 * 100),
        listingsYoY: ((newHouseData.listings - 3000) / 3000 * 100),
        priceMoM: ((newHouseData.avgPrice - 76200) / 76200 * 100), // 环比6月
        volumeMoM: ((newHouseData.dailyVolume - 43) / 43 * 100)
      }
    },

    // 新增市场信息
    marketInfo: {
      year: 2025,
      month: 7,
      season: '夏季淡季',
      trend: '稳定回暖',
      dataSource: 'fangdi.com.cn + 官方统计',
      note: '基于官方数据源的准确统计，二手房和新房数据已分离'
    }
  };
};

// 导出所有数据
export const mockData = {
  daily: generateDailyData(),
  monthly: generateMonthlyData(),
  quarterly: generateQuarterlyData(),
  yearly: generateYearlyData(),
  currentStats: generateCurrentStats()
};

export default mockData;
