# 上海房地产数据统计小程序

一个全面的上海房地产市场数据分析和可视化应用，提供实时市场监控、趋势分析和数据统计功能。

## 🏠 功能特性

### 核心数据指标
- **最新成交均价** - 实时显示上海房地产市场平均成交价格
- **当月累计成交量** - 展示当月累计房屋成交套数
- **总挂牌量** - 显示市场上可售房源总数
- **日成交量** - 每日房屋成交数量统计

### 时间维度分析
- **日度数据** - 每日详细成交数据
- **月度数据** - 月度汇总统计信息
- **季度数据** - 季度趋势分析
- **年度数据** - 年度市场回顾

### 对比分析
- **同比分析** - 与去年同期数据对比
- **环比分析** - 与上一期数据对比
- **趋势预测** - 基于历史数据的简单预测

### 市场热度指数
- **综合热度** - 基于多维度数据的市场活跃度指数
- **成交活跃度** - 成交量相关的活跃度指标
- **价格波动度** - 价格变化幅度指标
- **供需平衡度** - 供需关系平衡指标

## 🛠️ 技术栈

- **前端框架**: React 18
- **构建工具**: Vite
- **样式**: CSS3 + 响应式设计
- **图表**: 自定义SVG图表组件
- **图标**: Lucide React
- **日期处理**: date-fns
- **测试**: Vitest

## 📁 项目结构

```
shanghai-housing-stats/
├── src/
│   ├── components/          # React组件
│   │   ├── StatCard.jsx     # 统计卡片组件
│   │   ├── FilterButtons.jsx # 筛选按钮组件
│   │   ├── DataTable.jsx    # 数据表格组件
│   │   ├── MarketHeatIndex.jsx # 市场热度指数组件
│   │   ├── SimpleChart.jsx  # 简单图表组件
│   │   └── TrendChart.jsx   # 趋势图表组件
│   ├── data/               # 数据相关
│   │   └── mockData.js     # 模拟数据生成器
│   ├── services/           # 业务服务
│   │   └── statisticsService.js # 统计服务
│   ├── utils/              # 工具函数
│   │   └── dataUtils.js    # 数据处理工具
│   ├── types/              # 类型定义
│   │   └── index.js        # 数据类型定义
│   ├── test/               # 测试文件
│   │   └── dataUtils.test.js # 数据工具测试
│   ├── App.jsx             # 主应用组件
│   ├── main.jsx            # 应用入口
│   └── index.css           # 全局样式
├── public/                 # 静态资源
├── package.json            # 项目配置
├── vite.config.js          # Vite配置
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量（可选）
```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑 .env 文件，填入API密钥
# 如果不配置，将使用模拟数据
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 运行测试
```bash
npm test
```

### 5. 构建生产版本
```bash
npm run build
```

## 🔑 API密钥配置

为了获取真实数据，您需要配置以下API密钥：

### 必需的API密钥
1. **Tushare Token** (推荐)
   - 注册地址: https://tushare.pro/register
   - 免费账户每日有一定调用限制
   - 在 `.env` 文件中设置: `REACT_APP_TUSHARE_TOKEN=your_token`

2. **AKShare API** (可选)
   - 开源免费数据接口
   - 在 `.env` 文件中设置: `REACT_APP_AKSHARE_API_KEY=your_key`

### 可选的API密钥
- 上海市房屋管理局API（如果有官方合作）
- 其他房地产数据服务商API

### 无API密钥使用
如果没有配置任何API密钥，应用将：
1. 自动使用模拟数据模式
2. 显示高质量的模拟房地产数据
3. 保持所有功能正常运行

## 📊 数据说明

本应用支持**真实数据**和**模拟数据**两种模式：

### 🔴 真实数据模式
集成多个真实数据源，提供最新最准确的上海房地产数据：

#### 官方数据源
- **上海市房屋管理局** - 官方成交数据和统计信息
- **国家统计局** - 宏观房地产数据和价格指数

#### 第三方数据服务
- **AKShare** - 开源金融数据接口
- **Tushare** - 专业金融数据平台
- **公开数据平台** - 政府开放数据

#### 数据质量保证
- **多源验证** - 交叉验证确保数据准确性
- **实时监控** - 数据质量实时评估
- **自动修正** - 异常数据自动识别和修正
- **缓存机制** - 提高数据获取效率

### 🟡 模拟数据模式
当真实数据不可用时，使用高质量模拟数据：

- **价格数据**: 基于上海实际房价水平生成的模拟数据
- **成交量数据**: 模拟的日/月/季/年成交量数据
- **挂牌量数据**: 模拟的房源挂牌数量
- **趋势数据**: 包含季节性因素的趋势模拟

### 数据特点
- 包含合理的市场波动
- 考虑季节性因素影响
- 支持多时间维度聚合
- 提供同比环比计算
- 自动数据源切换
- 数据质量评分

## 🎯 核心功能

### 1. 实时数据监控
- 关键指标实时更新
- 直观的数据卡片展示
- 变化趋势可视化

### 2. 多维度分析
- 支持日/月/季/年多个时间维度
- 价格、成交量、面积等多个指标
- 灵活的数据筛选和切换

### 3. 趋势可视化
- 自定义SVG图表
- 折线图和柱状图支持
- 同比环比对比图表

### 4. 市场热度评估
- 综合多个维度的热度指数
- 直观的热度等级显示
- 详细的分项指标

## 🔧 自定义配置

### 修改数据源
在 `src/data/mockData.js` 中可以：
- 调整基础数据参数
- 修改趋势系数
- 自定义波动范围

### 添加新指标
1. 在 `src/types/index.js` 中定义新的指标类型
2. 在 `src/utils/dataUtils.js` 中添加处理函数
3. 在组件中使用新指标

### 样式定制
在 `src/index.css` 中可以：
- 修改主题颜色
- 调整布局样式
- 自定义响应式断点

## 📱 响应式设计

应用支持多种设备：
- **桌面端**: 完整功能展示
- **平板端**: 自适应布局
- **移动端**: 优化的移动体验

## 🧪 测试

项目包含完整的测试套件：
- 数据处理函数测试
- 组件单元测试
- 集成测试

运行测试：
```bash
npm test
```

## 📈 未来规划

- [ ] 集成真实数据API
- [ ] 添加更多图表类型
- [ ] 支持数据导出功能
- [ ] 添加用户偏好设置
- [ ] 实现数据缓存机制
- [ ] 添加移动端PWA支持

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
