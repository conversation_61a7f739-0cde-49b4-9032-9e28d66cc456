# 上海房地产数据统计小程序 - 部署指南

## 🎯 部署概述

本应用支持多种部署方式，可以根据您的需求选择合适的部署方案。

## 📋 部署前准备

### 1. 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器
- 现代浏览器支持

### 2. 获取API密钥（可选）
为了使用真实数据，建议获取以下API密钥：

#### 推荐的免费API
1. **Tushare** (推荐)
   - 注册地址: https://tushare.pro/register
   - 免费账户每日调用限制: 200次
   - 提供专业的金融和房地产数据

2. **AKShare** (开源)
   - GitHub: https://github.com/akfamily/akshare
   - 完全免费，开源数据接口
   - 提供丰富的中国金融数据

#### 官方数据源（如果可用）
- 上海市房屋管理局开放数据
- 国家统计局开放数据平台

## 🚀 部署方式

### 方式一：本地开发部署

```bash
# 1. 克隆或下载项目
cd "d:\vscode\project\Housing Statistics"

# 2. 安装依赖
npm install

# 3. 配置环境变量（可选）
cp .env.example .env
# 编辑 .env 文件，填入API密钥

# 4. 启动开发服务器
npm run dev

# 5. 访问应用
# 浏览器打开: http://localhost:3000
```

### 方式二：生产环境部署

```bash
# 1. 构建生产版本
npm run build

# 2. 部署到静态文件服务器
# 将 dist/ 目录下的文件部署到您的Web服务器
```

### 方式三：Docker部署

创建 `Dockerfile`:
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

部署命令:
```bash
# 构建Docker镜像
docker build -t shanghai-housing-stats .

# 运行容器
docker run -p 80:80 shanghai-housing-stats
```

### 方式四：云平台部署

#### Vercel部署
```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

#### Netlify部署
1. 将项目推送到GitHub
2. 在Netlify中连接GitHub仓库
3. 设置构建命令: `npm run build`
4. 设置发布目录: `dist`

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# 基础配置
REACT_APP_ENV=production
REACT_APP_ENABLE_REAL_DATA=true

# API密钥（根据需要配置）
REACT_APP_TUSHARE_TOKEN=your_tushare_token
REACT_APP_AKSHARE_API_KEY=your_akshare_key

# 缓存配置
REACT_APP_CACHE_EXPIRE_TIME=5
REACT_APP_DATA_UPDATE_INTERVAL=5

# 调试配置
REACT_APP_DEBUG_MODE=false
```

### 代理配置（解决跨域问题）

如果遇到跨域问题，可以配置代理服务器：

#### 开发环境代理
在 `vite.config.js` 中添加：
```javascript
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'https://api.example.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

#### 生产环境代理
使用Nginx反向代理：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass https://api.example.com/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔒 安全配置

### 1. API密钥安全
- 不要在代码中硬编码API密钥
- 使用环境变量管理敏感信息
- 定期轮换API密钥

### 2. HTTPS配置
生产环境务必使用HTTPS：
```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 其他配置...
}
```

### 3. 内容安全策略
在HTML中添加CSP头：
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
```

## 📊 监控和维护

### 1. 性能监控
- 使用浏览器开发者工具监控性能
- 配置错误报告服务（如Sentry）
- 监控API调用频率和成功率

### 2. 数据质量监控
应用内置数据质量监控功能：
- 实时数据质量评分
- 数据源可用性检查
- 自动故障转移

### 3. 日志记录
```javascript
// 在生产环境启用日志
if (process.env.NODE_ENV === 'production') {
  console.log = () => {}; // 禁用console.log
  // 使用专业日志服务
}
```

## 🚨 故障排除

### 常见问题

1. **数据无法加载**
   - 检查网络连接
   - 验证API密钥是否正确
   - 查看浏览器控制台错误信息

2. **跨域错误**
   - 配置代理服务器
   - 使用CORS代理服务
   - 联系数据提供方开启CORS

3. **性能问题**
   - 启用数据缓存
   - 减少API调用频率
   - 优化图表渲染

### 调试模式
启用调试模式查看详细信息：
```bash
REACT_APP_DEBUG_MODE=true npm run dev
```

## 📞 技术支持

如果在部署过程中遇到问题：

1. 查看项目README.md文件
2. 检查浏览器控制台错误信息
3. 查看网络请求状态
4. 验证环境变量配置

## 🔄 更新和维护

### 定期更新
```bash
# 更新依赖包
npm update

# 检查安全漏洞
npm audit

# 修复安全问题
npm audit fix
```

### 数据源维护
- 定期检查API密钥有效性
- 监控数据源服务状态
- 更新数据验证规则

---

**注意**: 本应用在没有API密钥的情况下也能正常运行，会自动使用高质量的模拟数据。
